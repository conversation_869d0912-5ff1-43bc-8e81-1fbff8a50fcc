#!/bin/bash

# 改进的流水线执行脚本
# 增加验证、反馈和容错机制

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
MAX_RETRIES=3
INTERACTIVE_MODE=${INTERACTIVE_MODE:-false}
VALIDATION_ENABLED=${VALIDATION_ENABLED:-true}

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 验证函数
validate_example() {
    local file="$1"
    log_info "验证例子质量..."
    
    # 检查文件存在性和基本格式
    if [ ! -f "$file" ]; then
        log_error "例子文件不存在: $file"
        return 1
    fi
    
    # 检查内容长度
    local word_count=$(wc -w < "$file")
    if [ "$word_count" -lt 100 ]; then
        log_warning "例子内容过短 ($word_count 词)，可能质量不佳"
        return 1
    fi
    
    # 检查是否包含关键元素
    if ! grep -q "步骤" "$file"; then
        log_warning "例子缺少步骤描述"
        return 1
    fi
    
    log_info "✅ 例子验证通过"
    return 0
}

validate_storyboard() {
    local file="$1"
    log_info "验证视觉故事板..."
    
    if [ ! -f "$file" ]; then
        log_error "故事板文件不存在: $file"
        return 1
    fi
    
    # 检查是否包含几何逻辑错误的关键词
    if grep -q "重排.*正方形" "$file" && grep -q "三角形.*滑动" "$file"; then
        log_warning "检测到可能的几何逻辑问题：三角形重排"
        if [ "$INTERACTIVE_MODE" = "true" ]; then
            echo "是否继续？(y/n)"
            read -r response
            if [ "$response" != "y" ]; then
                return 1
            fi
        else
            return 1
        fi
    fi
    
    log_info "✅ 故事板验证通过"
    return 0
}

# 带重试的执行函数
run_with_retry() {
    local cmd="$1"
    local description="$2"
    local validator="$3"
    local output_file="$4"
    
    for attempt in $(seq 1 $MAX_RETRIES); do
        log_step "$description (尝试 $attempt/$MAX_RETRIES)"
        
        if eval "$cmd"; then
            log_info "✅ 命令执行成功"
            
            # 如果提供了验证器，则进行验证
            if [ -n "$validator" ] && [ "$VALIDATION_ENABLED" = "true" ]; then
                if $validator "$output_file"; then
                    log_info "✅ $description 完成并验证通过"
                    return 0
                else
                    log_warning "验证失败，准备重试..."
                    continue
                fi
            else
                log_info "✅ $description 完成"
                return 0
            fi
        else
            log_error "❌ 命令执行失败 (尝试 $attempt/$MAX_RETRIES)"
            if [ $attempt -eq $MAX_RETRIES ]; then
                log_error "💥 $description 在 $MAX_RETRIES 次尝试后仍然失败"
                return 1
            fi
        fi
        
        sleep 2  # 重试前等待
    done
}

# 交互式检查点
interactive_checkpoint() {
    local file="$1"
    local description="$2"
    
    if [ "$INTERACTIVE_MODE" = "true" ]; then
        echo ""
        log_info "🔍 检查点: $description"
        echo "生成的文件: $file"
        echo "选项:"
        echo "  [Enter] 继续下一步"
        echo "  [e] 编辑文件"
        echo "  [v] 查看文件"
        echo "  [q] 退出"
        
        read -r choice
        case $choice in
            e)
                ${EDITOR:-nano} "$file"
                ;;
            v)
                less "$file"
                ;;
            q)
                log_info "用户选择退出"
                exit 0
                ;;
            *)
                log_info "继续执行..."
                ;;
        esac
    fi
}

# 获取主题配置
get_topic_from_config() {
    local config_file="config/config.yaml"
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    topic=$(python3 -c "
import yaml
try:
    with open('$config_file', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    print(config['example_explain']['topic'])
except Exception as e:
    print('ERROR: ' + str(e))
    exit(1)
")
    
    if [[ $topic == ERROR:* ]]; then
        log_error "解析配置文件失败: $topic"
        exit 1
    fi
    
    echo "$topic"
}

# 主函数
main() {
    echo "=================================================="
    log_info "🎬 开始执行改进的流水线"
    echo "=================================================="
    
    # 检查依赖
    if ! command -v python3 &> /dev/null; then
        log_error "需要安装 Python3"
        exit 1
    fi
    
    # 获取主题
    topic=$(get_topic_from_config)
    log_info "📋 主题: $topic"
    
    # 构建路径
    topic_dir="output/${topic}"
    example_explain_file="${topic_dir}/example_explain.md"
    vision_storyboard_file="${topic_dir}/vision_storyboard"
    
    # 清理并创建目录
    if [ -d "$topic_dir" ]; then
        rm -rf "$topic_dir"
    fi
    mkdir -p "$topic_dir"
    
    # 步骤1: 生成例子解释
    echo ""
    if run_with_retry \
        "python agents/example_explain_agent_refactor.py" \
        "步骤1: 生成例子解释" \
        "validate_example" \
        "$example_explain_file"; then
        
        interactive_checkpoint "$example_explain_file" "例子解释已生成"
    else
        log_error "💥 流水线在步骤 1 失败"
        exit 1
    fi
    
    # 步骤2: 生成视觉故事板
    echo ""
    if run_with_retry \
        "python agents/theorem_agents/vision_storyboard_agent.py '$example_explain_file' '$vision_storyboard_file'" \
        "步骤2: 生成视觉故事板" \
        "validate_storyboard" \
        "$vision_storyboard_file"; then
        
        interactive_checkpoint "$vision_storyboard_file" "视觉故事板已生成"
    else
        log_error "💥 流水线在步骤 2 失败"
        exit 1
    fi
    
    # 步骤3: 生成场景代码
    echo ""
    if run_with_retry \
        "python scripts/run_claude_code_router.py '$vision_storyboard_file' '$topic_dir/code/$topic'" \
        "步骤3: 生成场景代码" \
        "" \
        ""; then
        
        log_info "🎉 完整流水线执行成功!"
    else
        log_error "💥 流水线在步骤 3 失败"
        exit 1
    fi
    
    echo "=================================================="
    log_info "✨ 主题: $topic"
    log_info "📂 输出目录: $topic_dir"
    log_info "🎥 视频文件: media/videos/$topic/480p15/*.mp4"
    echo "=================================================="
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -i, --interactive    启用交互模式"
    echo "  -n, --no-validation  禁用验证"
    echo "  -r, --retries N      设置最大重试次数 (默认: 3)"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  INTERACTIVE_MODE     设置为 'true' 启用交互模式"
    echo "  VALIDATION_ENABLED   设置为 'false' 禁用验证"
    echo "  MAX_RETRIES         设置最大重试次数"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--interactive)
            INTERACTIVE_MODE=true
            shift
            ;;
        -n|--no-validation)
            VALIDATION_ENABLED=false
            shift
            ;;
        -r|--retries)
            MAX_RETRIES="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 检查脚本是否被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
