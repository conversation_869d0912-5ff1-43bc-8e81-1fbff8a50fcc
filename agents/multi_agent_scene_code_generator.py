"""
Multi-Agent Scene Code Generator (Simplified Architecture)

This module implements a simplified multi-agent architecture specifically designed for
generating correct Manim code. The architecture focuses on:

1. ManagerAgent: Task planning, flow control, and iteration management
2. CodeAgent: Unified code handling (generation, fixing, optimization) with fixed validation flow
3. MemoryAgent: Experience management (runs independently)

Key benefits:
- Simplified architecture with clear responsibilities
- Fixed validation flow ensures code quality (check + manim dryrun)
- Eliminates memory accumulation with minimal context per task
- Specialized for the specific goal: "generate correct Manim code"
"""

import json
import logging
import os
import subprocess
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional

from loguru import logger
from smolagents import CodeAgent, InferenceClientModel, OpenAIServerModel

# Import smolagents-compatible tools
from tools.code_agent_tools.smolagents_adapters import (
    bash_execute,
    check_code_issues,
    file_create,
    file_view,
    get_library_docs,
    list_files,
    replace_in_file,
    resolve_library_id,
    sequential_thinking,
)
from utils.common import Config

@dataclass
class CodeTask:
    """Unified task specification for code agent"""
    description: str
    output_path: str
    task_type: str = "generate"  # "generate" or "fix"
    current_code: Optional[str] = None
    error_info: Optional[str] = None
    requirements: Optional[List[str]] = None


@dataclass
class TaskResult:
    """Standardized result format for all agents"""
    success: bool
    file_path: Optional[str] = None
    error_message: Optional[str] = None
    validation_results: Optional[Dict[str, str]] = None


class MultiAgentSceneCodeGenerator:
    """
    Multi-agent system for Manim scene code generation with specialized agents.

    This architecture solves the memory accumulation problem by using specialized
    agents that only receive minimal, task-specific context.
    """

    def __init__(self, working_dir: Optional[str] = None):
        """
        Initialize the multi-agent system.

        Args:
            working_dir: Working directory for file operations (default: current directory)
        """
        self.working_dir = Path(working_dir or os.getcwd()).resolve()

        # Load configuration from config file (same as existing agent)
        config = Config().config.get("workflow", {}).get("code_agent", {})
        self.enable_sequential_thinking = config.get("enable_sequential_thinking", False)
        self.enable_get_docs = config.get("enable_get_docs", False)
        self.enable_memory = config.get("enable_memory", True)
        self.memory_file_path = config.get("memory_file_path", "multi_agent_memory.md")
        self.model_type = config.get("model", "google/gemini-2.5-flash-preview-05-20")
        self.memory_model_type = config.get("memory_model", "google/gemini-2.5-flash-lite-preview-06-17")
        self.summary_model_type = config.get("summary_model", "google/gemini-2.5-flash-lite-preview-06-17")
        self.max_iteration_per_step = config.get("max_iteration_per_step", 20)

        # Initialize memory system
        if self.enable_memory:
            self._ensure_memory_directory()

        # Initialize agents
        self._initialize_agents()

        logger.info(
            f"Multi-agent scene code generator initialized with working directory: {self.working_dir},"
            f" max_steps: {self.max_iteration_per_step},"
            f" sequential_thinking: {self.enable_sequential_thinking}, get_docs: {self.enable_get_docs},"
            f" memory: {self.enable_memory}"
        )

    def _create_smolagents_model(self, model: str = None):
        """Create a smolagents-compatible model from the existing model configuration."""
        try:
            # Get model configuration from config file
            config = Config().config
            model_config = config.get("model", {})
            api_config = model_config.get("api", {})

            # Extract OpenRouter configuration
            model_type = model or model_config.get("type", "google/gemini-2.5-flash-preview-05-20")
            api_key = api_config.get("openrouter_api_key")
            api_base = api_config.get("openrouter_api_base_url", "https://openrouter.ai/api/v1")
            max_tokens = model_config.get("max_tokens", 32768)

            if not api_key:
                raise RuntimeError("No OpenRouter API key found in config, using default InferenceClientModel")

            # Create OpenAI-compatible model for OpenRouter
            model = OpenAIServerModel(
                model_id=model_type,
                api_base=api_base,
                api_key=api_key,
                temperature=0.6,
                max_tokens=max_tokens,
            )

            logger.info(f"Created smolagents OpenAIServerModel with model: {model_type}")
            return model

        except Exception as e:
            logger.warning(f"Failed to create smolagents model from config: {e}")
            # Fallback to default model
            return InferenceClientModel()

    def _initialize_agents(self):
        """Initialize simplified agent architecture"""
        # Create models
        main_model = self._create_smolagents_model(self.model_type)
        memory_model = self._create_smolagents_model(self.memory_model_type)

        # Initialize simplified agents
        self.code_agent = self._create_code_agent(main_model)
        self.memory_agent = self._create_memory_agent(memory_model)
        self.manager_agent = self._create_manager_agent(main_model)

        logger.info("Simplified multi-agent system initialized successfully")

    def _ensure_memory_directory(self):
        """Ensure memory directory exists"""
        memory_dir = self.working_dir / "memory"
        memory_dir.mkdir(parents=True, exist_ok=True)

    def _get_memory_file_path(self) -> Path:
        """Get the path to the memory file."""
        return self.working_dir / "memory" / self.memory_file_path

    def _to_absolute_path(self, path: str) -> str:
        """Convert relative path to absolute path"""
        path_obj = Path(path)
        if path_obj.is_absolute():
            return str(path_obj)
        return str(self.working_dir / path_obj)

    def _create_code_agent(self, model):
        """Create unified code agent with fixed validation flow"""
        tools = [
            file_view,
            file_create,
            replace_in_file,
            check_code_issues,
            bash_execute,
        ]

        if self.enable_sequential_thinking:
            tools.append(sequential_thinking)

        if self.enable_get_docs:
            tools.extend([resolve_library_id, get_library_docs])

        return CodeAgent(
            tools=tools,
            model=model,
            name="code_agent",
            description="统一的Manim代码处理专家agent",
            instructions="""# Manim代码处理专家

你是专门处理Manim代码的统一agent，负责代码生成、修复和优化。

## 核心职责
1. 根据需求生成Manim代码
2. 修复代码中的问题
3. 执行固定的验证流程
4. 确保代码质量

## 固定验证流程（必须执行）
每次处理代码后，必须按顺序执行：
1. 使用check_code_issues检查代码问题
2. 使用bash_execute执行"manim --dry_run --progress_bar none -a [文件路径]"
3. 分析检查结果
4. 如有问题，立即修复并重新验证
5. 确保代码通过所有检查

## 工作原则
- 专注于生成正确的Manim代码
- 遵循Manim最新API规范
- 代码结构清晰，注释完整
- 必须通过代码检查和manim dry run
- 如不确定API用法，主动查询文档

你会收到精简的任务描述，请按照固定流程确保代码质量。""",
            max_steps=self.max_iteration_per_step,
        )



    def _create_memory_agent(self, model):
        """Create specialized memory management agent"""
        tools = [file_view, file_create, replace_in_file]

        return CodeAgent(
            tools=tools,
            model=model,
            name="memory_agent",
            description="专门管理编程经验记忆的agent",
            instructions="""# 编程经验记忆管理专家

你是专门管理编程开发经验记忆的专家agent。你的任务是从对话历史中提取有价值的经验并更新记忆文件。

## 核心职责
1. 分析编程开发对话历史
2. 提取有价值的经验和教训
3. 识别常见问题和解决方案
4. 更新和维护记忆文件

## 记忆内容包括
- 常见错误类型和修复方法
- Manim API使用技巧和最佳实践
- 代码模式和模板
- 调试和优化经验
- 工具使用技巧

## 工作原则
- 专注于可复用的经验和知识
- 保持记忆内容的结构化和可搜索性
- 定期整理和去重
- 优先记录解决方案而不是问题描述

你会收到对话历史，请自主决定如何更新记忆文件以积累有价值的编程经验。""",
            max_steps=10,
        )

    def _create_manager_agent(self, model):
        """Create manager agent for task coordination"""
        return CodeAgent(
            tools=[file_view, list_files, bash_execute],  # 基础工具
            model=model,
            managed_agents=[
                self.code_agent,
                self.memory_agent,
            ],
            name="manager",
            description="任务规划和流程控制agent",
            instructions="""# 任务管理者

你是专门负责Manim代码生成任务的管理者，负责任务规划和流程控制。

## 团队成员
1. **code_agent**: 统一的代码处理专家（生成、修复、验证）
2. **memory_agent**: 经验管理专家

## 核心职责
1. 接收用户需求，进行任务分析
2. 为code_agent准备精简的任务描述
3. 管理迭代流程（最多3次尝试）
4. 判断任务完成条件
5. 协调整体工作流程

## 工作流程
1. 分析用户需求
2. 调用code_agent执行代码任务（包含固定验证流程）
3. 检查结果，决定是否需要重试
4. 管理迭代直到成功或达到最大次数

## 重要原则
- 专注于高级规划，不处理具体代码
- 为code_agent提供清晰的任务描述
- 维护简洁的上下文，避免memory累积
- 让专业的agent处理专业的事情

你的目标是确保生成正确的Manim代码。""",
            max_steps=self.max_iteration_per_step,
        )

    def execute_code_task(self, task: CodeTask) -> TaskResult:
        """
        Execute unified code task using the code agent with fixed validation flow.

        Args:
            task: CodeTask with description, output path, and optional context

        Returns:
            TaskResult with success status and validation results
        """
        try:
            # Prepare task description for unified code agent
            if task.task_type == "generate":
                task_description = f"""请生成Manim代码：

## 需求描述
{task.description}

## 输出文件路径
{task.output_path}

{f"## 具体要求\n" + chr(10).join(f"- {req}" for req in task.requirements) if task.requirements else ""}

## 固定验证流程（必须执行）
1. 生成代码后，使用check_code_issues检查代码问题
2. 执行"manim --dry_run --progress_bar none -a {task.output_path}"
3. 如有问题，立即修复并重新验证
4. 确保代码通过所有检查

请按照固定流程确保代码质量。"""

            else:  # task_type == "fix"
                task_description = f"""请修复以下代码问题：

## 文件路径
{task.output_path}

## 当前代码
```python
{task.current_code or "请先查看文件内容"}
```

## 错误信息
{task.error_info or "请分析代码问题"}

## 固定验证流程（必须执行）
1. 分析并修复代码问题
2. 使用check_code_issues检查代码问题
3. 执行"manim --dry_run --progress_bar none -a {task.output_path}"
4. 如仍有问题，继续修复并重新验证
5. 确保代码通过所有检查

请按照固定流程确保代码质量。"""

            logger.info(f"Starting {task.task_type} task for {task.output_path}")

            # Call unified code agent with minimal context
            result = self.code_agent.run(task_description)

            logger.info(f"Code {task.task_type} task completed successfully")

            return TaskResult(
                success=True,
                file_path=task.output_path,
                validation_results={"agent_result": result}
            )

        except Exception as e:
            logger.error(f"Code {task.task_type} failed: {e}")
            return TaskResult(
                success=False,
                error_message=str(e)
            )



    def update_memory(self, conversation_history: str) -> TaskResult:
        """
        Update memory using the specialized memory agent.

        Args:
            conversation_history: Complete conversation history from current session

        Returns:
            TaskResult with success status
        """
        if not self.enable_memory:
            return TaskResult(success=True, result="Memory disabled")

        try:
            memory_file_path = self.working_dir / "programming_memory.md"

            task_description = f"""请分析以下编程开发对话历史并更新记忆文件：

## 对话历史
```
{conversation_history}
```

## 记忆文件路径
{memory_file_path}

请从对话中提取有价值的编程经验、常见问题解决方案、API使用技巧等，并更新记忆文件。
如果记忆文件不存在，请创建一个新的。保持内容结构化和易于搜索。"""

            logger.info("Starting memory update task")

            # Call specialized memory agent
            result = self.memory_agent.run(task_description)

            logger.info("Memory update task completed successfully")

            return TaskResult(
                success=True,
                result=result,
                files_modified=[str(memory_file_path)]
            )

        except Exception as e:
            logger.error(f"Memory update failed: {e}")
            return TaskResult(
                success=False,
                result="",
                error_message=str(e)
            )

    def get_status(self) -> Dict[str, Any]:
        """
        Get current status of the multi-agent system.

        Returns:
            Dictionary with system status information
        """
        return {
            "system_type": "simplified_multi_agent",
            "architecture": "Manager + Code + Memory",
            "agents": {
                "manager": {"name": self.manager_agent.name, "status": "active", "role": "Task planning and flow control"},
                "code_agent": {"name": self.code_agent.name, "status": "active", "role": "Unified code handling with fixed validation"},
                "memory_agent": {"name": self.memory_agent.name, "status": "active", "role": "Experience management"},
            },
            "configuration": {
                "model_type": self.model_type,
                "memory_model_type": self.memory_model_type,
                "enable_sequential_thinking": self.enable_sequential_thinking,
                "enable_get_docs": self.enable_get_docs,
                "enable_memory": self.enable_memory,
                "max_steps": self.max_iteration_per_step,
                "working_dir": str(self.working_dir),
            },
            "key_features": {
                "unified_code_agent": "Single agent handles generation, fixing, and validation",
                "fixed_validation_flow": "Automatic code check + manim dry run for every task",
                "memory_accumulation_solution": "Minimal context per task, no history accumulation",
                "specialized_for": "Generate correct Manim code"
            }
        }

    def run_with_manager(self, task_description: str) -> str:
        """
        Run a task using the manager agent for coordination.

        This method allows the manager to decide which specialized agent to use.

        Args:
            task_description: High-level task description

        Returns:
            Result from the coordinated agents
        """
        try:
            logger.info("Starting task with manager coordination")

            # Let the manager agent coordinate the task
            result = self.manager_agent.run(task_description)

            logger.info("Manager-coordinated task completed successfully")
            return result

        except Exception as e:
            logger.error(f"Manager-coordinated task failed: {e}")
            return f"Task failed: {str(e)}"

    def execute_programming_task(
        self, task_description: str, output_file: str = None, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Execute programming task using multi-agent framework with iteration history.
        This method maintains compatibility with the existing agent interface.

        Args:
            task_description: Description of the programming task to complete
            output_file: Output file path for the generated code (optional)
            max_iterations: Maximum number of iterations for quality improvement

        Returns:
            Path to generated code file if applicable, or success message, or None on failure
        """
        output_path = self._to_absolute_path(output_file) if output_file else None

        try:
            logger.info("Starting multi-agent programming task execution...")
            current_iteration_error = ""

            for iteration in range(max_iterations):
                logger.info(f"Iteration {iteration + 1}/{max_iterations}")

                # Use unified code agent for all iterations
                if iteration == 0:
                    # First iteration: generate new code
                    task = CodeTask(
                        description=task_description,
                        output_path=output_path,
                        task_type="generate",
                        requirements=["使用Manim最新API", "确保代码可以直接运行", "包含适当的注释"]
                    )
                else:
                    # Subsequent iterations: fix existing code
                    current_code = None
                    if output_path and Path(output_path).exists():
                        current_code = Path(output_path).read_text(encoding="utf-8")

                    task = CodeTask(
                        description=f"修复代码问题: {task_description}",
                        output_path=output_path,
                        task_type="fix",
                        current_code=current_code,
                        error_info=current_iteration_error
                    )

                # Execute unified code task
                result = self.execute_code_task(task)
                if not result.success:
                    logger.error(f"Code task failed: {result.error_message}")
                    current_iteration_error = result.error_message or f"Code {task.task_type} failed"
                    continue

                logger.info(f"Agent completed iteration {iteration + 1}")

                # Update memory with conversation history from this iteration
                if self.enable_memory:
                    try:
                        # Create a summary of this iteration for memory
                        iteration_summary = f"""
Iteration {iteration + 1}:
Task: {task_description}
Agent used: {'CodeGenerator' if iteration == 0 else 'CodeFixer'}
Result: {'Success' if result.success else 'Failed'}
Error: {current_iteration_error if current_iteration_error else 'None'}
"""
                        self.update_memory(iteration_summary)
                        logger.debug(f"Updated memory after iteration {iteration + 1}")
                    except Exception as e:
                        logger.warning(f"Failed to update memory after iteration {iteration + 1}: {e}")

                # Check if task was completed successfully
                task_completed = True
                if output_path and Path(output_path).exists():
                    # Check for code issues if there's an output file
                    from tools.code_agent_tools.smolagents_adapters import check_code_issues, bash_execute
                    issues_result = check_code_issues([output_path], "error")
                    logger.info(f"Iteration {iteration + 1}, Code issues: {issues_result}")

                    if "No issues found" in issues_result or "❌" not in issues_result:
                        manim_dryrun_result = bash_execute(f"manim --dry_run --progress_bar none -a {output_path}")
                        if manim_dryrun_result.startswith(
                            "Command executed successfully"
                        ) or manim_dryrun_result.startswith("Command execution successfully"):
                            logger.success(f"Programming task completed after {iteration + 1} iterations")
                            return output_path
                        else:
                            logger.error(f"Manim dry run failed: {manim_dryrun_result}")
                            task_completed = False
                            current_iteration_error = manim_dryrun_result
                    else:
                        task_completed = False
                        current_iteration_error = issues_result
                else:
                    # For tasks without specific output files, assume completion based on agent execution
                    logger.success(f"Programming task completed after {iteration + 1} iterations")
                    return "Task completed successfully"

                # If not successful, continue to next iteration
                if not task_completed and iteration < max_iterations - 1:
                    logger.info(f"Iteration {iteration + 1} not successful, continuing...")

            # Return result based on whether there was an output file
            if output_path:
                return output_path if Path(output_path).exists() else None
            else:
                return "Task execution completed"

        except Exception as e:
            logger.error(f"Error in programming task execution: {e}")
            return None

    def generate_manim_code_enhanced(
        self, scene_description: str, output_file: str, max_iterations: int = 3
    ) -> Optional[str]:
        """
        Backward compatibility method for Manim code generation.

        Args:
            scene_description: Description of the scene to generate
            output_file: Output file path for the generated code
            max_iterations: Maximum number of iterations for quality improvement

        Returns:
            Path to generated and debugged code file, or None on failure
        """
        return self.execute_programming_task(scene_description, output_file, max_iterations)

    def render_manim_code(self, code_file: str, quality: str = "l") -> Optional[str]:
        """
        Render Manim code to video.
        This method is identical to the existing agent implementation.
        """
        try:
            code_path = Path(code_file)
            if not code_path.exists():
                logger.error(f"Code file not found: {code_file}")
                return None

            # Prepare manim command
            cmd = [
                "manim",
                str(code_path),
                "--quality",
                quality,
            ]

            logger.info(f"Rendering Manim code: {' '.join(cmd)}")

            # Run manim command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                resolution = {
                    "l": "480p15",
                    "m": "720p30",
                    "h": "1080p60",
                    "q": "1440p60",
                    "k": "2160p60",
                }[quality]
                output_path = Path("media") / "videos" / code_path.stem / resolution
                # Find the generated video file
                video_files = list(output_path.glob("*.mp4"))
                if video_files:
                    # Sort by modification time (newest first) and take the most recent one
                    video_file = max(video_files, key=lambda f: f.stat().st_mtime)
                    logger.success(f"Video rendered successfully: {video_file}")
                    return str(video_file)
                else:
                    logger.error(f"No video file found at {output_path} after rendering")
                    return None
            else:
                error_msg = result.stderr or result.stdout
                logger.error(f"Manim rendering failed: {error_msg}")
                raise subprocess.CalledProcessError(result.returncode, cmd, error_msg)

        except subprocess.TimeoutExpired:
            logger.error("Manim rendering timed out")
            return None
        except Exception as e:
            logger.error(f"Failed to render Manim code: {e}")
            raise


# Alias for backward compatibility
MultiAgentSceneCodeGenerationToolkit = MultiAgentSceneCodeGenerator
