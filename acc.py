import sys
import os
sys.path.insert(0, os.getcwd())
from prompts.professional_science_template import ProfessionalScienceTemplate
from manim import *
import numpy as np

class Word2VecAlgorithm(ProfessionalScienceTemplate):
    def create_bullet_list(self, items):
        """Helper function to create VGroup of Text objects from string list"""
        return VGroup(*[Text(item, font_size=20) for item in items]).arrange(DOWN, aligned_edge=LEFT)
    
    def construct(self):
        # 必须调用
        self.setup_background()
        
        # 阶段1：数据准备与词汇表构建
        self.create_stage1_content()
        self.wait(2)
        
        # 阶段2：词汇表构建结果
        self.create_stage2_content()
        self.wait(2)
        
        # 阶段3：窗口滑动与样本生成
        self.create_stage3_content()
        self.wait(2)
        
        # 阶段4：样本生成结果
        self.create_stage4_content()
        self.wait(2)
        
        # 阶段5：词语向量化
        self.create_stage5_content()
        self.wait(2)
        
        # 阶段6：神经网络训练与向量更新
        self.create_stage6_content()
        self.wait(2)
        
        # 阶段7：神经网络训练结果
        self.create_stage7_content()
        self.wait(2)
        
        # 阶段8：最终向量
        self.create_stage8_content()
        self.wait(2)
        
        # 阶段9：余弦相似度计算
        self.create_stage9_content()
        self.wait(2)
        
        # 阶段10：相似度查询结果
        self.create_stage10_content()
        self.wait(3)

    def create_stage1_content(self):
        # 标题区域
        title = self.create_title_region_content("AI学语言")
        self.play(Write(title))
        self.region_elements['title'] = title
        
        # 步骤区域
        step = self.create_step_region_content("步骤1：数据准备")
        self.play(Write(step))
        self.region_elements['step'] = step
        
        # 主内容区域 - 故事书内容
        storybook_title = Text("故事书内容：").scale(0.8)
        line1 = Text("1. 狗 喜欢 骨头。").scale(0.7)
        line2 = Text("2. 猫 喜欢 鱼。").scale(0.7)
        line3 = Text("3. 狗 追 猫。").scale(0.7)
        line4 = Text("4. 猫 怕 狗。").scale(0.7)
        
        storybook_group = VGroup(storybook_title, line1, line2, line3, line4).arrange(DOWN, aligned_edge=LEFT, buff=0.2)
        
        main = self.create_main_region_content(storybook_group)
        self.play(main.animate.shift(LEFT * 3), storybook_group.animate.shift(RIGHT * 3))
        self.region_elements['main'] = main
        
        # 左辅助区域
        left_aux = self.create_left_auxiliary_content("背景", 
            self.create_bullet_list(["• AI小明学语言", "• 一本小故事书"]))
        self.play(Write(left_aux))
        self.region_elements['left_aux'] = left_aux
        
        # 右辅助区域
        right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 语料", "• 词汇表"]))
        self.play(Write(right_aux))
        self.region_elements['right_aux'] = right_aux
        
        # 结果区域
        result = self.create_result_region_content("小明尝试理解人类语言")
        self.play(Write(result))
        self.region_elements['result'] = result

    def create_stage2_content(self):
        # 步骤区域保持不变
        
        # 主内容区域 - 词汇表
        vocab_title = Text("词汇表 (Vocabulary)").scale(0.7)
        vocab_table = Table([
            ["索引", "词语"],
            ["0", "狗"],
            ["1", "喜欢"],
            ["2", "骨头"],
            ["3", "猫"]
        ]).scale(0.5)
        
        vocab_group = VGroup(vocab_title, vocab_table).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(vocab_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 语料", "• 词汇表", "• 独一无二的词"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("构建6个词的词汇表")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

    def create_stage3_content(self):
        # 步骤区域更新
        new_step = self.create_step_region_content("步骤2：样本生成")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 高亮显示的故事书
        highlighted_title = Text("故事书内容：").scale(0.8).set_color(YELLOW)
        h_line1 = Text("1. 狗 喜欢 骨头。").scale(0.7)
        h_line2 = Text("2. 猫 喜欢 鱼。").scale(0.7)
        h_line3 = Text("3. 狗 追 猫。").scale(0.7)
        
        # 高亮部分词语
        h_line1[0][3:5].set_color(RED)  # 高亮"狗"
        h_line1[0][6:8].set_color(BLUE)  # 高亮"喜欢"
        
        highlighted_group = VGroup(highlighted_title, h_line1, h_line2, h_line3).arrange(DOWN, aligned_edge=LEFT, buff=0.2)
        
        new_main = self.create_main_region_content(highlighted_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 左辅助区域更新
        new_left_aux = self.create_left_auxiliary_content("操作", 
            self.create_bullet_list(["• 设置观察窗口", "• 中心词", "• 周围词"]))
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 滑动窗口", "• 学习样本"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("生成10对中心词-周围词样本")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

    def create_stage4_content(self):
        # 主内容区域 - 样本表格
        sample_title = Text("学习样本").scale(0.7)
        sample_table = Table([
            ["中心词", "周围词"],
            ["狗", "喜欢"],
            ["喜欢", "狗"],
            ["喜欢", "骨头"]
        ]).scale(0.5)
        
        sample_group = VGroup(sample_title, sample_table).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(sample_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 左辅助区域更新
        new_left_aux = self.create_left_auxiliary_content("操作", 
            self.create_bullet_list(["• 中心词", "• 周围词", "• 配对"]))
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 中心词-周围词", "• 学习样本集"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("产出丰富的学习样本")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

    def create_stage5_content(self):
        # 步骤区域更新
        new_step = self.create_step_region_content("步骤3：词语向量化")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 初始向量表格
        initial_title = Text("初始词向量").scale(0.7)
        initial_table = Table([
            ["词语", "初始向量"],
            ["狗", "[0.1, 0.5, -0.2]"],
            ["喜欢", "[-0.3, 0.8, 0.1]"],
            ["骨头", "[0.6, -0.1, 0.4]"]
        ]).scale(0.5)
        
        initial_group = VGroup(initial_title, initial_table).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(initial_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 左辅助区域更新
        new_left_aux = self.create_left_auxiliary_content("操作", 
            self.create_bullet_list(["• 词语", "• 随机分配位置"]))
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 词向量", "• 多维空间"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("词语获得初始3维向量表示")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

    def create_stage6_content(self):
        # 步骤区域更新
        new_step = self.create_step_region_content("步骤4：神经网络训练")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 训练参数
        lr_text = Text("学习率 α = 0.01").scale(0.6)
        softmax_formula = MathTex(r"P(Y|X) = \frac{\exp(V_Y \cdot V_X)}{\sum_{w} \exp(V_w \cdot V_X)}").scale(0.5)
        loss_formula = MathTex(r"L = -\sum \log P(Y|X)").scale(0.6)
        
        training_group = VGroup(lr_text, softmax_formula, loss_formula).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(training_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 左辅助区域更新
        new_left_aux = self.create_left_auxiliary_content("操作", 
            self.create_bullet_list(["• 计算可能性", "• 调整向量"]))
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 神经网络", "• 梯度下降", "• 学习率"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("通过模型迭代优化词向量")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

    def create_stage7_content(self):
        # 主内容区域 - 损失值表格
        loss_title = Text("损失值变化").scale(0.7)
        loss_table = Table([
            ["训练轮次", "平均损失值"],
            ["1", "2.5"],
            ["50", "0.7"],
            ["100", "0.2"]
        ]).scale(0.5)
        
        loss_group = VGroup(loss_title, loss_table).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(loss_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 左辅助区域更新
        new_left_aux = self.create_left_auxiliary_content("操作", 
            self.create_bullet_list(["• 反复训练", "• 重复调整"]))
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 迭代", "• 损失值降低"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("训练100轮，损失值显著降低")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

    def create_stage8_content(self):
        # 步骤区域更新
        new_step = self.create_step_region_content("步骤N：词语相似度查询")
        self.play(ReplacementTransform(self.region_elements['step'], new_step))
        self.region_elements['step'] = new_step
        
        # 主内容区域 - 最终向量表格
        final_title = Text("最终词向量").scale(0.7)
        final_table = Table([
            ["词语", "最终向量"],
            ["狗", "[0.8, 0.2, -0.1]"],
            ["猫", "[-0.1, 0.6, 0.8]"],
            ["骨头", "[0.7, 0.3, -0.2]"]
        ]).scale(0.5)
        
        final_group = VGroup(final_title, final_table).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(final_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 左辅助区域更新
        new_left_aux = self.create_left_auxiliary_content("应用", 
            self.create_bullet_list(["• 词语比较", "• 空间距离"]))
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 最终向量", "• 语义关系"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("学习完成，词语拥有语义身份卡")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

    def create_stage9_content(self):
        # 主内容区域 - 相似度公式
        similarity_formula = MathTex(r"\text{cos}(\mathbf{A}, \mathbf{B}) = \frac{\mathbf{A} \cdot \mathbf{B}}{||\mathbf{A}|| \cdot ||\mathbf{B}||}").scale(0.6)
        
        formula_group = VGroup(similarity_formula)
        new_main = self.create_main_region_content(formula_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 左辅助区域更新
        new_left_aux = self.create_left_auxiliary_content("应用", 
            self.create_bullet_list(["• 向量对比", "• 余弦相似度"]))
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 相似度", "• 距离"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("问小明，谁和狗最像？")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result

    def create_stage10_content(self):
        # 主内容区域 - 相似度查询结果
        similarity_title = Text("相似度查询结果").scale(0.7)
        similarity_table = Table([
            ["查询", "答案", "相似度"],
            ["狗", "猫", "0.95"],
            ["狗", "骨头", "0.88"]
        ]).scale(0.5)
        
        similarity_group = VGroup(similarity_title, similarity_table).arrange(DOWN, buff=0.3)
        new_main = self.create_main_region_content(similarity_group)
        self.play(ReplacementTransform(self.region_elements['main'], new_main))
        self.region_elements['main'] = new_main
        
        # 左辅助区域更新
        new_left_aux = self.create_left_auxiliary_content("查询", 
            self.create_bullet_list(["• 狗是谁", "• 最接近的词"]))
        self.play(ReplacementTransform(self.region_elements['left_aux'], new_left_aux))
        self.region_elements['left_aux'] = new_left_aux
        
        # 右辅助区域更新
        new_right_aux = self.create_right_auxiliary_content("概念", 
            self.create_bullet_list(["• 数字身份卡", "• 亲戚关系"]))
        self.play(ReplacementTransform(self.region_elements['right_aux'], new_right_aux))
        self.region_elements['right_aux'] = new_right_aux
        
        # 结果区域更新
        new_result = self.create_result_region_content("小明：猫和骨头都像狗！")
        self.play(ReplacementTransform(self.region_elements['result'], new_result))
        self.region_elements['result'] = new_result