#网页/app前端描述
1 顶部Logo和slgon: 复杂知识变得易懂
2 中上部分视频生成画布区
    素材上传：本地素材文件（PDF、DOC、PPT、Excel、Image）、在线素材（网址链接、Arxiv、github）
    指令区域：Chat对话框，如果有素材，指令只对素材整体或者部分生效，如果没有素材就无中生有
    开始生成：确定开始按钮，点击后给一个进度条，给出预估时间
3 中部及下部视频内容区
    TAB区域: 内容分类TAB，一级直接显示，二级标签可以下拉，AI原理、AI论文、AI项目、计算机知识、数学知识、AI解题、AI读书、AI办公。其中AI论文可以分视觉、Agent、LLM、金融等二级标签
    内容区域: 根据TAB，显示对应类目多列视频的卡片，卡片下面有点赞、关注、评论外显
    视频卡片：点击之后可以调起视频播放，在屏幕中间区域，点击叉掉或者视频外区域退出播放；增加评论外显，改成“你来创造”，用户输入修改意见，可生成一个修改的视频
4 左侧导航栏
    默认收缩，点击展开
    个人生产: 包括生成过的视频、以及生成过视频的播放表现，受欢迎程度
    个人消费: 浏览过、点赞、关注等行为记录
    个人账户: 余额和充值
    个人等级: 激励等级，账号名字
    平台设置: 颜色等
    联系和合作：联系方式，合作模式
    付费模式：进入付费

#功能满足
核心想展现功能: 任何复杂内容都能易懂
##用户动线:
    1 生产: 打开网页->Chat输入主题 + 上传素材（可选）-> 生成视频->视频发布（可选）->视频下载（暂不支持）->生产达到阈值提醒付费
    2 消费: 打开网页->内容区筛选类目（可选，默认是全品类）->点击视频->点赞关注评价->视频分享链接（可选）->退出播放
    3 账号: 打开网页->banner签到积分->登陆账号->侧边栏->生产视频消费信息红点提醒，等级和积分变化->生产超阈值（提醒充值）->充值

###生产核心:
    - Chat框内可以输入内容：主题和目的，比如“分析这篇文章内容”，“解释下word2vec算法原理”等
    - 本地上传文件, 拖拽上传文件，支持各种类型文件（目前不支持视频），chat框给出一个默认的输入“根据这个文件生成一个视频”
    - 输入链接，比如arxiv,github,blog等，chat框给出一个默认的输入
    - 点击视频生成，判断生产权限，默认一天生产2条，付费或者积分逻辑判断，如果不生产权限了，弹出鼓励充值付费
    - 点击视频生成，有权限的，给出生成进度条和倒计时，生成完成后，平台上自动打开视频，视频下面给出“是否别人可见”选择，选择之后插入到视频内容区第一个

###消费核心:
    - 类目刷选功能，下面视频跟着变化，注意可以每类缓存一屏视频？便于快速切换TAB能很快看到视频list
    - 点击视频后，调起视频播放器，能展现创作者，观看次数，点赞，评论，分享按钮
    - 具备实时记录观看次数，点赞次数，评论内容（前期比较少可以不展开），分享生成链接可分享
    - 退出能力，点击视频外区域关闭视频，退回到主场景

###账号核心:
    - 用户注册和登录功能，支持手机号/微信号/邮箱登陆，默认账号随机名字，需要保证密码安全
    - 支付能力，付费收款支付宝/微信、需要资金安全
    - 积分体系：签到积分、分享积分、互动积分，积分可以换视频生成次数
    - 个人中心：历史生成内容信息，包括反馈次数，反馈信息（反馈的人物暂不做）


技术支持：
1 一个路由大模型：
    -识别输入主题、目的，根据素材的判断类型，根据用户基本画像，补充定位和风格侧重；
    -安全风险识别，对有安全风险、返回一个“抱歉，这个问题还不能生成视频， AI费曼还需要学习”
    -能力处理边界识别，能做的领域其他领域，包括不能下载的URL，解析出错的文件，非能力范围，比如还不支持视频，返回一个对应的提示
2 视频的推拉流:
    -视频生成后，要不要编解码，放到CDN，更快的视频传输和拉取速度
3 视频的交互数据记录:
    -记录观看次数、点赞、评论、转发等内容记录和日志记录
4 后端如何选择框架，支持高并发请求
    -后端设计好请求日志格式，高并发时分流
5 个人中心数据记录
    -用户画像
    -账号内容
    -生产内容



