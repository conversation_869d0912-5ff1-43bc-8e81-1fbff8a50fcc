#!/usr/bin/env python3
"""
测试BPE算法动画场景
"""

import subprocess
import sys
import os

def test_manim_render():
    """测试Manim渲染"""
    try:
        # 运行manim命令
        result = subprocess.run([
            'manim', '-pql', 'scene_vision_bpe_new.py', 'BPEAlgorithmScene'
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        print("=== Manim渲染结果 ===")
        print(f"返回码: {result.returncode}")
        print(f"标准输出:\n{result.stdout}")
        if result.stderr:
            print(f"标准错误:\n{result.stderr}")
        
        # 检查视频文件是否存在
        video_path = "media/videos/scene_vision_bpe_new/480p15/BPEAlgorithmScene.mp4"
        if os.path.exists(video_path):
            file_size = os.path.getsize(video_path)
            print(f"\n✅ 视频文件已生成: {video_path}")
            print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
            return True
        else:
            print(f"\n❌ 视频文件未找到: {video_path}")
            return False
            
    except Exception as e:
        print(f"❌ 渲染过程中出现错误: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    print("=== 检查依赖项 ===")
    
    # 检查manim
    try:
        result = subprocess.run(['manim', '--version'], capture_output=True, text=True)
        print(f"✅ Manim版本: {result.stdout.strip()}")
    except FileNotFoundError:
        print("❌ Manim未安装")
        return False
    
    # 检查模板文件
    template_path = "prompts/professional_science_template.py"
    if os.path.exists(template_path):
        print(f"✅ 模板文件存在: {template_path}")
    else:
        print(f"❌ 模板文件未找到: {template_path}")
        return False
    
    # 检查场景文件
    scene_path = "scene_vision_bpe_new.py"
    if os.path.exists(scene_path):
        print(f"✅ 场景文件存在: {scene_path}")
    else:
        print(f"❌ 场景文件未找到: {scene_path}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎬 BPE算法动画场景测试")
    print("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        print("\n❌ 依赖项检查失败")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # 测试渲染
    if test_manim_render():
        print("\n🎉 测试成功！BPE算法动画已成功生成")
        print("\n📹 视频文件位置:")
        print("   media/videos/scene_vision_bpe_new/480p15/BPEAlgorithmScene.mp4")
        print("\n🚀 可以使用以下命令重新生成:")
        print("   manim -pql scene_vision_bpe_new.py BPEAlgorithmScene")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
