import os
import sys
from typing import Any, Optional

from camel.toolkits.base import BaseToolkit
from camel.toolkits.function_tool import FunctionTool
from camel.utils import dependencies_required
from loguru import logger


class ArxivRecorderToolkit(BaseToolkit):
    """
    一个用于录制ArXiv论文页面平滑滚动视频的工具包。

    功能：
        - 自动化录制ArXiv论文页面的平滑滚动视频。
        - 支持自定义录制时长、分辨率、帧率、滚动平滑度、聚焦标题等参数。
        - 适合论文展示、学术分享等场景。

    方法：
        record_arxiv_video(url, output_path, duration, width, height, fps, smooth_factor, title_focus, zoom_factor, abstract_pause)

    返回：
        dict，包含视频保存路径、参数、执行状态等。
    """

    @dependencies_required("selenium", "cv2", "numpy", "webdriver_manager")
    def __init__(self, timeout: Optional[float] = None) -> None:
        super().__init__(timeout=timeout)

    def record_arxiv_video(
        self,
        url: str,
        output_path: str = "output.mp4",
        duration: int = 8,
        width: int = 1920,
        height: int = 1080,
        fps: int = 15,
        smooth_factor: float = 0.2,
        title_focus: int = 4,
        zoom_factor: float = 2.0,
        abstract_pause: float = 0.0,
    ) -> dict[str, Any]:
        """
        录制ArXiv论文页面的平滑滚动视频。

        参数：
            url (str): ArXiv论文页面URL。
            output_path (str): 输出视频文件路径。
            duration (int): 录制时长（秒）。
            width (int): 视频宽度（像素）。
            height (int): 视频高度（像素）。
            fps (int): 帧率。
            smooth_factor (float): 滚动平滑度。
            title_focus (int): 标题聚焦时长（秒）。
            zoom_factor (float): 聚焦区域缩放系数。
            abstract_pause (float): 在摘要部分暂停的时间（秒）。
        返回：
            dict: 包含视频路径、参数、状态等信息。
        """
        try:
            # 使用sys.path确保能导入到utils模块
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from utils import arxiv_recorder

            logger.info(f"开始录制ArXiv论文页面: {url}")
            logger.info(f"视频将保存到: {output_path}")

            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 创建命令行参数类似的对象
            class Args:
                def __init__(self):
                    self.url: str = ""
                    self.output_path: str = ""
                    self.duration: int = 0
                    self.width: int = 0
                    self.height: int = 0
                    self.fps: int = 0
                    self.smooth_factor: float = 0.0
                    self.title_focus: int = 0
                    self.zoom_factor: float = 0.0
                    self.abstract_pause: float = 0.0

            args = Args()
            args.url = url
            args.output_path = output_path
            args.duration = duration
            args.width = width
            args.height = height
            args.fps = fps
            args.smooth_factor = smooth_factor
            args.title_focus = title_focus
            args.zoom_factor = zoom_factor
            args.abstract_pause = abstract_pause

            # 使用arxiv_recorder模块的功能
            try:
                # 设置WebDriver
                logger.info("设置WebDriver...")
                driver = arxiv_recorder.setup_driver(width, height)

                # 导航到URL
                logger.info(f"加载 {url}...")
                driver.get(url)

                # 等待页面加载
                logger.info("等待页面加载完成...")
                import time
                time.sleep(3)

                # 捕获帧，聚焦标题并滚动页面
                logger.info(f"开始捕获，总时长 {duration} 秒，帧率 {fps} FPS...")
                frames = arxiv_recorder.capture_arxiv_paper(
                    driver,
                    duration,
                    fps,
                    width,
                    height,
                    smooth_factor,
                    title_focus,
                    zoom_factor,
                    abstract_pause
                )

                # 如果捕获到帧，创建视频
                if frames:
                    logger.info(f"创建视频（共 {len(frames)} 帧）...")
                    arxiv_recorder.create_video(frames, output_path, fps, width, height)
                    logger.info(f"视频已保存到 {output_path}")

                    return {
                        "status": "success",
                        "output_path": output_path,
                        "params": {
                            "url": url,
                            "duration": duration,
                            "width": width,
                            "height": height,
                            "fps": fps,
                            "smooth_factor": smooth_factor,
                            "title_focus": title_focus,
                            "zoom_factor": zoom_factor,
                            "abstract_pause": abstract_pause,
                        },
                        "message": f"视频已保存到 {output_path}",
                    }
                else:
                    logger.error("未能捕获任何帧，无法创建视频")
                    return {"status": "error", "output_path": output_path, "error": "未能捕获任何帧，无法创建视频"}
            except Exception as e:
                logger.error(f"执行过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return {"status": "error", "output_path": output_path, "error": str(e)}
            finally:
                # 清理资源
                if "driver" in locals() and driver:
                    try:
                        driver.quit()
                        logger.info("WebDriver已关闭")
                    except Exception as e:
                        logger.error(f"关闭WebDriver时出错: {e}")

        except Exception as e:
            logger.error(f"录制ArXiv视频失败: {e}")
            import traceback
            traceback.print_exc()
            return {"status": "error", "output_path": output_path, "error": str(e)}

    def get_tools(self):
        """
        返回工具列表，供大模型自动发现和调用。
        """
        return [
            FunctionTool(self.record_arxiv_video),
        ]


if __name__ == "__main__":
    toolkit = ArxivRecorderToolkit()
    result = toolkit.record_arxiv_video(
        url="https://arxiv.org/abs/2506.12479",
        output_path="arxiv_paper.mp4",
        duration=8,
        width=1920,
        height=1080,
        fps=15,
        smooth_factor=0.2,
        title_focus=4,
        zoom_factor=2.0,
        abstract_pause=0.0,
    )
    print(result) 