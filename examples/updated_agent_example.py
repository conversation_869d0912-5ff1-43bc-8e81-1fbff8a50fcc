#!/usr/bin/env python3
"""
Updated Scene Code Generation Agent Example

This example demonstrates the updated scene_code_generation_agent.py with multi-agent support.
Shows how the agent now uses the multi-agent framework by default to solve memory accumulation.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def demonstrate_updated_agent():
    """Demonstrate the updated agent with multi-agent framework"""
    print("🚀 Updated Scene Code Generation Agent Demo")
    print("=" * 50)
    
    try:
        from agents.scene_code_generation_agent import EnhancedSceneCodeGenerationToolkit
        
        # Initialize the updated agent (now uses multi-agent by default)
        print("Initializing updated agent...")
        agent = EnhancedSceneCodeGenerationToolkit(
            working_dir="output/updated_agent_demo"
        )
        
        print(f"✅ Agent initialized successfully!")
        print(f"  - Framework: {agent.agent_framework}")
        print(f"  - Working directory: {agent.working_dir}")
        
        # Show framework benefits
        if agent.agent_framework == "multi_agent":
            print("🎯 Multi-agent benefits:")
            print("  ✅ Solves memory accumulation problem")
            print("  ✅ Specialized agents for different tasks")
            print("  ✅ Better performance and lower costs")
            print("  ✅ Improved debugging and maintenance")
            
            # Get multi-agent status
            if hasattr(agent, '_multi_agent_toolkit') and agent._multi_agent_toolkit:
                status = agent._multi_agent_toolkit.get_status()
                print(f"  ✅ System type: {status.get('system_type', 'unknown')}")
                print(f"  ✅ Active agents: {len(status.get('agents', {}))}")
                for agent_name, agent_info in status.get('agents', {}).items():
                    print(f"    - {agent_name}: {agent_info.get('status', 'unknown')}")
        
        return agent
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return None


def demonstrate_code_generation(agent):
    """Demonstrate code generation with the updated agent"""
    print("\n📝 Code Generation Demo")
    print("-" * 30)
    
    if not agent:
        print("❌ No agent available")
        return
    
    # Create a simple animation description
    scene_description = """
创建一个简单的Manim动画场景：

1. 显示一个蓝色的圆形
2. 圆形从左边移动到右边
3. 移动过程中颜色从蓝色变为红色
4. 添加标题文字 "Simple Animation"
5. 整个动画持续3秒

要求：
- 使用Manim最新API
- 代码结构清晰
- 包含适当注释
"""
    
    output_file = "output/updated_agent_demo/simple_animation.py"
    
    print(f"Scene description: {len(scene_description)} characters")
    print(f"Output file: {output_file}")
    print(f"Max iterations: 3")
    
    try:
        print("\n🔧 Generating code with multi-agent framework...")
        print("(This uses specialized agents to avoid memory accumulation)")
        
        # Use the same interface as before - no changes needed!
        result = agent.generate_manim_code_enhanced(
            scene_description=scene_description,
            output_file=output_file,
            max_iterations=3
        )
        
        if result:
            print(f"✅ Code generation successful!")
            print(f"Generated file: {result}")
            
            # Show that the file was created
            if Path(result).exists():
                file_size = Path(result).stat().st_size
                print(f"File size: {file_size} bytes")
                
                # Show first few lines
                with open(result, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:10]
                    print(f"First 10 lines:")
                    for i, line in enumerate(lines, 1):
                        print(f"  {i:2d}: {line.rstrip()}")
                    if len(lines) == 10:
                        print("  ...")
            
            return result
        else:
            print(f"❌ Code generation failed")
            return None
            
    except Exception as e:
        print(f"❌ Code generation error: {e}")
        return None


def demonstrate_video_rendering(agent, code_file):
    """Demonstrate video rendering with the updated agent"""
    print("\n🎬 Video Rendering Demo")
    print("-" * 30)
    
    if not agent or not code_file:
        print("❌ No agent or code file available")
        return
    
    try:
        print(f"Rendering code file: {code_file}")
        print("Quality: low (for faster demo)")
        
        # Use the same interface as before - no changes needed!
        video_result = agent.render_manim_code(code_file, quality="l")
        
        if video_result:
            print(f"✅ Video rendering successful!")
            print(f"Video file: {video_result}")
            
            if Path(video_result).exists():
                file_size = Path(video_result).stat().st_size
                print(f"Video size: {file_size} bytes")
        else:
            print(f"⚠️  Video rendering skipped (manim not available or other issues)")
            
    except Exception as e:
        print(f"⚠️  Video rendering error (expected without manim): {e}")


def demonstrate_framework_comparison():
    """Demonstrate different framework options"""
    print("\n🔄 Framework Comparison")
    print("-" * 30)
    
    frameworks = [
        ("multi_agent", "Solves memory accumulation, specialized agents"),
        ("smolagents", "Advanced tool integration, iterative debugging"),
        ("camel", "Reliable baseline, structured workflow")
    ]
    
    print("Available frameworks:")
    for framework, description in frameworks:
        print(f"  - {framework}: {description}")
    
    print(f"\nCurrent default: multi_agent (recommended)")
    print(f"To change framework, update config.yaml:")
    print(f"  workflow.code_agent.agent_framework: 'multi_agent' | 'smolagents' | 'camel'")


def main():
    """Run the complete demonstration"""
    print("🎯 Updated Scene Code Generation Agent - Complete Demo")
    print("=" * 60)
    
    # Step 1: Initialize agent
    agent = demonstrate_updated_agent()
    
    # Step 2: Generate code
    code_file = demonstrate_code_generation(agent)
    
    # Step 3: Render video
    demonstrate_video_rendering(agent, code_file)
    
    # Step 4: Show framework options
    demonstrate_framework_comparison()
    
    print("\n" + "=" * 60)
    print("🎉 Demo completed successfully!")
    print("\nKey takeaways:")
    print("✅ Updated agent now uses multi-agent framework by default")
    print("✅ Memory accumulation problem is solved")
    print("✅ Same interface as before - no code changes needed")
    print("✅ Better performance, lower costs, improved debugging")
    print("✅ Framework can be changed via config if needed")
    
    print(f"\nGenerated files:")
    if code_file and Path(code_file).exists():
        print(f"  - Code: {code_file}")
    
    print(f"\nNext steps:")
    print(f"1. Use the updated agent in your existing code")
    print(f"2. Enjoy the benefits of multi-agent architecture")
    print(f"3. Monitor the improved performance and reduced costs")


if __name__ == "__main__":
    main()
