# Manim代码生成规范

## 基础要求
- 继承 `ProfessionalScienceTemplate` 类，重写 `construct` 方法
- 使用标准区域接口：
  - `self.create_title_region_content(string)` - 标题区域
  - `self.create_step_region_content(string)` - 步骤区域  
  - `self.create_main_region_content(VGroup)` - 主内容区域
  - `self.create_result_region_content(string)` - 结果区域

## 关键规范
1. **元素组织**：所有元素必须在 `create_main_region_content` 之前定义完成，包括后期出现的高亮框、说明文字等，并且都添加到main_group中
2. **布局定位**：VGroup内元素使用相对位置（`next_to`、`align_to`、`arrange`），禁用绝对位置
3. **动画控制**：关键逻辑分步展示，元素切换使用 `ReplacementTransform()`，而不是fadein+fadeout，**重要**禁止用copy()函数
4. **文字限制**：标题≤8字，步骤≤12字，辅助标题≤6字，结果≤20字

## 代码模板
```python
import sys, os
sys.path.insert(0, os.getcwd())
from prompts.professional_science_template import ProfessionalScienceTemplate

class MyExample(ProfessionalScienceTemplate):
    def construct(self):
        self.setup_background()
        self.create_stage1_content()
        self.create_stage2_content()

    def create_stage1_content(self):
        # 区域内容
        title = self.create_title_region_content("标题")
        step = self.create_step_region_content("步骤")
        
        # 定义所有主区域元素
        element1 = Text("主要内容")
        element2 = Rectangle()
        highlight_box = Rectangle(color=RED)
        
        # 组织到VGroup并创建主区域
        main_group = VGroup(element1, element2, highlight_box)
        main = self.create_main_region_content(main_group)
        
        # 动画播放
        self.play(FadeIn(title))
        self.region_elements['title'] = title
        self.play(FadeIn(step))
        self.region_elements['step'] = step
        self.play(ReplacementTransform(self.region_elements['main'], main))
        self.region_elements['main'] = main
```