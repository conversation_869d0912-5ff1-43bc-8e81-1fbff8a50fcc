from manim import *
import numpy as np

class RNNVisualization(Scene):
    def construct(self):
        # 设置标题
        title = Text("RNN网络结构与Hidden State变化", font_size=36)
        title.to_edge(UP)
        self.play(Write(title))
        self.wait(1)
        
        # 创建RNN单元
        def create_rnn_cell(position, label):
            # RNN单元主体
            cell = RoundedRectangle(
                width=1.5, height=1.2, 
                corner_radius=0.1,
                fill_color=BLUE,
                fill_opacity=0.3,
                stroke_color=BLUE,
                stroke_width=2
            )
            cell.move_to(position)
            
            # 标签
            cell_label = Text(label, font_size=20, color=WHITE)
            cell_label.move_to(cell.get_center())
            
            return VGroup(cell, cell_label)
        
        # 创建输入节点
        def create_input_node(position, label):
            circle = Circle(radius=0.3, fill_color=GREEN, fill_opacity=0.5)
            circle.move_to(position)
            text = Text(label, font_size=16)
            text.move_to(circle.get_center())
            return VGroup(circle, text)
        
        # 创建输出节点
        def create_output_node(position, label):
            circle = Circle(radius=0.3, fill_color=RED, fill_opacity=0.5)
            circle.move_to(position)
            text = Text(label, font_size=16)
            text.move_to(circle.get_center())
            return VGroup(circle, text)
        
        # 创建hidden state节点
        def create_hidden_node(position, label, color=YELLOW):
            rect = RoundedRectangle(
                width=0.8, height=0.5,
                corner_radius=0.1,
                fill_color=color,
                fill_opacity=0.6,
                stroke_color=color,
                stroke_width=2
            )
            rect.move_to(position)
            text = Text(label, font_size=14)
            text.move_to(rect.get_center())
            return VGroup(rect, text)
        
        # 时间步位置
        time_positions = [LEFT * 4, ORIGIN, RIGHT * 4]
        
        # 创建三个时间步的RNN单元
        rnn_cells = []
        input_nodes = []
        output_nodes = []
        hidden_nodes = []
        
        for i, pos in enumerate(time_positions):
            # RNN单元
            cell = create_rnn_cell(pos, f"RNN")
            rnn_cells.append(cell)
            
            # 输入节点
            input_pos = pos + DOWN * 2
            input_node = create_input_node(input_pos, f"x{i+1}")
            input_nodes.append(input_node)
            
            # 输出节点
            output_pos = pos + UP * 2
            output_node = create_output_node(output_pos, f"y{i+1}")
            output_nodes.append(output_node)
            
            # Hidden state节点
            if i == 0:
                hidden_pos = pos + LEFT * 2
                hidden_node = create_hidden_node(hidden_pos, "h0", GRAY)
            else:
                hidden_pos = time_positions[i-1] + RIGHT * 2
                hidden_node = create_hidden_node(hidden_pos, f"h{i}", YELLOW)
            hidden_nodes.append(hidden_node)
        
        # 添加最终的hidden state
        final_hidden = create_hidden_node(time_positions[-1] + RIGHT * 2, "h3", YELLOW)
        hidden_nodes.append(final_hidden)
        
        # 创建箭头连接
        arrows = []
        
        # 输入到RNN的箭头
        for i in range(3):
            arrow = Arrow(
                input_nodes[i].get_top(),
                rnn_cells[i].get_bottom(),
                buff=0.1,
                color=GREEN
            )
            arrows.append(arrow)
        
        # RNN到输出的箭头
        for i in range(3):
            arrow = Arrow(
                rnn_cells[i].get_top(),
                output_nodes[i].get_bottom(),
                buff=0.1,
                color=RED
            )
            arrows.append(arrow)
        
        # Hidden state的箭头
        for i in range(3):
            # 从hidden state到RNN
            arrow = Arrow(
                hidden_nodes[i].get_right(),
                rnn_cells[i].get_left(),
                buff=0.1,
                color=YELLOW
            )
            arrows.append(arrow)
            
            # 从RNN到下一个hidden state
            if i < 3:
                arrow = Arrow(
                    rnn_cells[i].get_right(),
                    hidden_nodes[i+1].get_left(),
                    buff=0.1,
                    color=YELLOW
                )
                arrows.append(arrow)
        
        # 时间步标签
        time_labels = []
        for i, pos in enumerate(time_positions):
            label = Text(f"t={i+1}", font_size=24, color=PURPLE)
            label.move_to(pos + DOWN * 3)
            time_labels.append(label)
        
        # 动画序列
        # 1. 显示网络结构
        self.play(
            *[Create(cell) for cell in rnn_cells],
            *[Create(node) for node in input_nodes],
            *[Create(node) for node in output_nodes],
            run_time=2
        )
        
        # 2. 显示初始hidden state
        self.play(Create(hidden_nodes[0]), run_time=1)
        
        # 3. 逐步展示每个时间步
        for i in range(3):
            # 显示时间步标签
            self.play(Write(time_labels[i]), run_time=0.5)
            
            # 显示输入箭头和处理过程
            input_arrow_idx = i
            self.play(Create(arrows[input_arrow_idx]), run_time=0.5)
            
            # 显示hidden state箭头
            hidden_to_rnn_idx = 6 + i * 2
            self.play(Create(arrows[hidden_to_rnn_idx]), run_time=0.5)
            
            # RNN处理动画（闪烁效果）
            self.play(
                rnn_cells[i].animate.set_fill(opacity=0.8),
                run_time=0.3
            )
            self.play(
                rnn_cells[i].animate.set_fill(opacity=0.3),
                run_time=0.3
            )
            
            # 显示输出箭头
            output_arrow_idx = 3 + i
            self.play(Create(arrows[output_arrow_idx]), run_time=0.5)
            
            # 显示下一个hidden state
            if i < 2:
                rnn_to_hidden_idx = 7 + i * 2
                self.play(
                    Create(arrows[rnn_to_hidden_idx]),
                    Create(hidden_nodes[i+1]),
                    run_time=1
                )
            else:
                # 最后一步，显示最终hidden state
                self.play(
                    Create(arrows[-1]),
                    Create(hidden_nodes[-1]),
                    run_time=1
                )
            
            self.wait(0.5)
        
        # 4. 添加说明文字
        explanation = VGroup(
            Text("RNN网络特点:", font_size=20, color=WHITE),
            Text("• 共享参数权重", font_size=16, color=WHITE),
            Text("• Hidden state在时间步间传递", font_size=16, color=WHITE),
            Text("• 能够处理序列数据", font_size=16, color=WHITE),
        ).arrange(DOWN, aligned_edge=LEFT)
        explanation.to_edge(DOWN + LEFT)
        
        self.play(Write(explanation), run_time=2)
        self.wait(2)
        
        # 5. 高亮显示hidden state的传递路径
        hidden_path = VGroup(*[arrows[6], arrows[7], arrows[8], arrows[9], arrows[10], arrows[11]])
        self.play(
            hidden_path.animate.set_color(ORANGE).set_stroke(width=4),
            run_time=1
        )
        
        # 添加hidden state变化的数学表示
        math_text = MathTex(
            r"h_t = \tanh(W_{hh}h_{t-1} + W_{xh}x_t + b_h)",
            font_size=24
        )
        math_text.to_edge(RIGHT + DOWN)
        self.play(Write(math_text), run_time=2)
        
        self.wait(3)

# 运行动画
if __name__ == "__main__":
    scene = RNNVisualization()
    scene.render()
