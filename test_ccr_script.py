#!/usr/bin/env python3
"""
测试claude-code-router脚本
"""

import sys
import os
from pathlib import Path

# 添加scripts目录到路径
current_dir = Path(__file__).parent
scripts_dir = current_dir / "scripts"
sys.path.append(str(scripts_dir))

# 导入我们的脚本
from run_claude_code_router import process_scene_with_ccr

def test_ccr_integration():
    """测试claude-code-router集成"""
    print("🧪 测试claude-code-router集成")
    print("=" * 50)
    
    # 测试场景文件
    scene_file = "test_scene_description.txt"
    output_file = "test_hello_manim"
    
    # 检查场景文件是否存在
    if not os.path.exists(scene_file):
        print(f"❌ 场景文件不存在: {scene_file}")
        return False
    
    # 运行处理
    result = process_scene_with_ccr(scene_file, output_file)
    
    # 输出结果
    print("\n📊 测试结果:")
    print(f"代码文件: {result.get('final_code_path')}")
    print(f"视频文件: {result.get('final_video_path')}")
    print(f"成功状态: {result.get('success')}")
    
    return result.get('success', False)

if __name__ == "__main__":
    success = test_ccr_integration()
    if success:
        print("\n🎉 测试成功！")
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
