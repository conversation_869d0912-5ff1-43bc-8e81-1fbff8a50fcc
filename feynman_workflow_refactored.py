#!/usr/bin/env python3
"""
重构后的自动工作流脚本

该脚本执行以下步骤：
1. 从各种材料源开始解析内容（GitHub、PDF、网页、本地文件等）
2. 生成项目/论文介绍素材（支持录屏等扩充功能）
3. 生成动画脚本故事板
4. 渲染最终视频

用法：
python feynman_workflow_refactored.py [--config 配置文件路径] [--source 材料源类型] [--purpose 视频目的]
"""

import argparse
import os
import re
import subprocess
import sys
import threading
import time

import yaml
from loguru import logger


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="重构后的统一材料源工作流")
    parser.add_argument("--config", default="config/config.yaml", help="配置文件路径")
    parser.add_argument(
        "--source",
        choices=["github", "pdf", "webpage", "local_file", "chat"],
        help="指定材料源类型 (如果不指定，将根据配置文件自动判断)",
    )
    parser.add_argument("--purpose", help="视频目的描述 (覆盖配置文件中的设置)")
    parser.add_argument("--chat", action="store_true", help="启用chat模式，通过用户输入的purpose生成视频")

    # 素材扩充选项
    parser.add_argument("--enable-video", action="store_true", help="强制启用录屏扩充")
    parser.add_argument("--disable-video", action="store_true", help="禁用录屏扩充")
    parser.add_argument("--enable-image", action="store_true", help="启用图片生成扩充")
    parser.add_argument("--enable-audio", action="store_true", help="启用音频合成扩充")

    return parser.parse_args()


def load_config(config_path):
    """加载配置文件"""
    logger.info(f"正在加载配置文件: {config_path}")
    try:
        with open(config_path, encoding="utf-8") as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        sys.exit(1)


def get_active_material_source(config, specified_source=None):
    """获取活跃的材料源配置"""
    material_config = config.get("material", {})
    sources = material_config.get("sources", {})

    if specified_source:
        # 如果指定了材料源，检查该源是否存在配置
        if specified_source not in sources:
            logger.error(f"配置文件中未找到指定的材料源: {specified_source}")
            sys.exit(1)
        source_config = sources[specified_source]
        source_config["type"] = specified_source
        return source_config

    # 自动检测启用的材料源
    active_sources = []
    for source_type, source_config in sources.items():
        if source_config.get("enabled", False):
            source_config["type"] = source_type
            active_sources.append(source_config)

    if len(active_sources) == 0:
        logger.error("配置文件中没有启用任何材料源")
        sys.exit(1)
    elif len(active_sources) > 1:
        logger.warning("配置文件中启用了多个材料源，只使用第一个")
        enabled_types = [s["type"] for s in active_sources]
        logger.warning(f"启用的材料源: {enabled_types}")

    return active_sources[0]


def validate_source_config(source_config):
    """验证材料源配置"""
    source_type = source_config["type"]

    if source_type == "github":
        if not source_config.get("url"):
            logger.error("GitHub源配置缺少url字段")
            return False
    elif source_type == "pdf":
        if not source_config.get("url"):
            logger.error("PDF源配置缺少url字段")
            return False
    elif source_type == "webpage":
        if not source_config.get("url"):
            logger.error("网页源配置缺少url字段")
            return False
    elif source_type == "local_file":
        if not source_config.get("path"):
            logger.error("本地文件源配置缺少path字段")
            return False
        if not os.path.exists(source_config["path"]):
            logger.error(f"本地文件不存在: {source_config['path']}")
            return False
    elif source_type == "chat":
        if not source_config.get("purpose"):
            logger.error("Chat源配置缺少purpose字段")
            return False
    else:
        logger.error(f"不支持的材料源类型: {source_type}")
        return False

    return True


def extract_project_name(source_config):
    """从源配置中提取项目名称"""
    source_type = source_config.get("type", "")

    if source_type == "github":
        url = source_config.get("url", "")
        if url:
            # 从GitHub URL提取项目名
            match = re.search(r"github\.com/([\w\-]+)/([\w\-]+)", url)
            if match:
                return match.group(2)
        return "github_project"

    elif source_type == "pdf":
        url = source_config.get("url", "")
        if url and "arxiv.org" in url:
            # 提取arxiv论文ID作为项目名
            match = re.search(r"(\d{4}\.\d{5})", url)
            if match:
                return match.group(1)
        return "pdf_content"

    elif source_type == "webpage":
        url = source_config.get("url", "")
        if url:
            # 从网页URL提取项目名
            from urllib.parse import urlparse

            parsed_url = urlparse(url)
            hostname = parsed_url.hostname or "webpage"
            path = parsed_url.path.strip("/").replace("/", "_") or "content"
            return f"{hostname}_{path}"[:50]  # 限制长度
        return "webpage_content"

    elif source_type == "local_file":
        path = source_config.get("path", "")
        if path:
            # 从本地文件路径提取项目名
            filename = os.path.basename(path)
            return os.path.splitext(filename)[0] or "local_content"
        return "local_content"

    elif source_type == "chat":
        # Chat模式使用时间戳作为项目名
        import time

        timestamp = time.strftime("%Y%m%d_%H%M%S")
        return f"chat_{timestamp}"

    else:
        return "unknown_project"


def get_purpose_from_config(config, source_config, args_purpose=None):
    """从配置文件或命令行参数获取视频目的"""
    if args_purpose:
        return args_purpose

    # 优先使用材料源特定的purpose
    if source_config.get("purpose"):
        return source_config["purpose"]

    # 回退到全局默认purpose
    material_config = config.get("material", {})
    return material_config.get("default_purpose", "提供简单易懂的内容分析，面向一般受众，以客观中立的风格呈现")


def run_command(cmd, desc, verbose=True):
    """运行命令并实时显示日志输出"""
    logger.info(f"开始执行: {desc}")
    logger.info("分隔线======================================")
    if verbose:
        logger.info(f"执行命令: {cmd}")

    try:
        process = subprocess.Popen(
            cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1
        )

        def handle_output(stream, is_error=False):
            for line in stream:
                line = line.rstrip()
                if not line:
                    continue

                if is_error:
                    logger.warning(line)
                else:
                    logger.info(line)

        stdout_thread = threading.Thread(target=handle_output, args=(process.stdout, False))
        stderr_thread = threading.Thread(target=handle_output, args=(process.stderr, True))

        stdout_thread.start()
        stderr_thread.start()

        stdout_thread.join()
        stderr_thread.join()

        return_code = process.wait()

        logger.info("======================================")
        logger.info(f"命令执行完成: {desc}")

        if return_code == 0:
            return True
        else:
            logger.error(f"命令执行失败，返回码: {return_code}")
            return False
    except Exception as e:
        logger.error(f"命令执行异常: {str(e)}")
        logger.info("======================================")
        return False


def ensure_output_directory(project_name):
    """确保输出目录存在"""
    output_dir = f"output/{project_name}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir


def process_github_source(source_config, project_name, output_dir):
    """处理GitHub源 - 使用重构后的代理"""
    analysis_file = f"{output_dir}/project_analysis.md"
    if os.path.exists(analysis_file):
        logger.info(f"GitHub分析文件已存在: {analysis_file}，跳过步骤1")
        return analysis_file

    # 使用重构后的GitHub代理
    github_url = source_config["url"]
    step1_cmd = f"python -m agents.github_source_agent_refactored --repo {github_url}"

    if not run_command(step1_cmd, "步骤1: 使用重构代理分析GitHub项目"):
        logger.error("GitHub源处理失败")
        return None

    if not os.path.exists(analysis_file):
        logger.error(f"GitHub分析文件未生成: {analysis_file}")
        return None

    return analysis_file


def process_pdf_source(source_config, project_name, output_dir):
    """处理PDF源"""
    pdf_url = source_config["url"]
    analysis_file = f"{output_dir}/{project_name}.md"

    if os.path.exists(analysis_file):
        logger.info(f"PDF分析文件已存在: {analysis_file}，跳过步骤1")
        # 检查文件是否包含URL，如果没有则添加
        try:
            with open(analysis_file, encoding="utf-8") as f:
                content = f.read()
            if pdf_url not in content:
                logger.info("为已存在的文件添加原始PDF URL信息")
                # 在文件开头添加URL信息
                url_header = f"**原始PDF链接**: {pdf_url}\n\n"
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write(url_header + content)
        except Exception as e:
            logger.warning(f"无法更新PDF文件URL信息: {e}")
        return analysis_file

    step1_cmd = f"python -c \"from tools.pdf_toolkit import PDFToolkit; result = PDFToolkit().extract_pdf('{pdf_url}', 'output'); print(f'PDF处理完成: {{result.get(\\\"markdown_file\\\", \\\"未知\\\")}}')\""
    if not run_command(step1_cmd, "步骤1: 下载并解析PDF文件"):
        logger.error("PDF源处理失败")
        return None

    if not os.path.exists(analysis_file):
        logger.error(f"PDF分析文件未生成: {analysis_file}")
        return None

    # 在生成的markdown文件中添加原始PDF URL
    try:
        with open(analysis_file, encoding="utf-8") as f:
            content = f.read()

        # 在文件开头添加URL信息，确保录屏功能可以检测到
        url_header = f"**原始PDF链接**: {pdf_url}\n\n"

        with open(analysis_file, "w", encoding="utf-8") as f:
            f.write(url_header + content)

        logger.info(f"已在PDF分析文件中添加原始URL: {pdf_url}")

    except Exception as e:
        logger.error(f"无法在PDF文件中添加URL信息: {e}")

    return analysis_file


def process_webpage_source(source_config, project_name, output_dir):
    """处理网页源"""
    analysis_file = f"{output_dir}/webpage_analysis.md"

    if os.path.exists(analysis_file):
        logger.info(f"网页分析文件已存在: {analysis_file}，跳过步骤1")
        return analysis_file

    try:
        # 导入并初始化WebpageToolkit
        logger.info("正在初始化WebpageToolkit...")
        from tools.webpage_toolkit import WebpageToolkit

        # 获取网页提取配置
        extract_config = source_config.get("extract_config", {})

        # 创建toolkit实例，使用配置文件中的参数或默认值
        toolkit_params = {
            "timeout": extract_config.get("timeout", 30),
            "use_cache": True,  # 启用缓存以提高效率
            "user_agent": extract_config.get(
                "user_agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            ),
        }

        # 配置图片过滤参数
        if extract_config.get("download_images", True):
            image_filter = extract_config.get("image_filter", {})
            toolkit_params["image_filter_config"] = {
                "skip_keywords": image_filter.get(
                    "skip_keywords", ["badge", "icon", "logo", "license", "button", "avatar"]
                ),
                "min_size_kb": image_filter.get("min_size_kb", 5),
            }
        else:
            # 如果不下载图片，设置严格的过滤规则
            toolkit_params["image_filter_config"] = {
                "skip_keywords": ["*"],  # 跳过所有图片
                "min_size_kb": 99999999,  # 设置极大的最小尺寸
            }

        logger.info(f"WebpageToolkit配置: {toolkit_params}")
        toolkit = WebpageToolkit(**toolkit_params)

        # 获取网页URL
        webpage_url = source_config["url"]
        logger.info(f"开始提取网页内容: {webpage_url}")

        # 使用WebpageToolkit提取网页内容
        base_output_dir = os.path.dirname(output_dir)  # 获取output目录

        result = toolkit.extract_webpage(url=webpage_url, output_base_dir=base_output_dir, project_name=project_name)

        if "error" in result:
            logger.error(f"网页内容提取失败: {result['error']}")
            # 创建错误提示文件
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write("# 网页内容分析\n\n")
                f.write(f"**源URL:** {webpage_url}\n\n")
                f.write("**状态:** 提取失败\n\n")
                f.write(f"**错误信息:** {result['error']}\n\n")
                f.write("请检查URL是否有效，或者网站是否存在访问限制。\n")
            return analysis_file

        # 检查生成的markdown文件
        generated_markdown = result.get("markdown_file")
        if not generated_markdown or not os.path.exists(generated_markdown):
            logger.error(f"生成的markdown文件不存在: {generated_markdown}")
            return None

        # 检查生成的文件路径和目标路径是否相同
        if os.path.abspath(generated_markdown) == os.path.abspath(analysis_file):
            logger.info(f"生成的文件已经在目标位置: {analysis_file}")
        else:
            # 只有路径不同时才需要复制
            import shutil

            shutil.copy2(generated_markdown, analysis_file)
            logger.info(f"已将文件从 {generated_markdown} 复制到 {analysis_file}")

        # 记录提取结果
        logger.info("网页内容提取成功:")
        logger.info(f"  - 页面标题: {result.get('title', '未知')}")
        logger.info(f"  - 媒体文件数量: {result.get('media_count', 0)}")
        logger.info(f"  - 输出目录: {result.get('output_dir', '未知')}")
        logger.info(f"  - 分析文件: {analysis_file}")

        return analysis_file

    except ImportError as e:
        logger.error(f"无法导入WebpageToolkit: {e}")
        logger.error("请确保tools/webpage_toolkit.py文件存在且依赖已安装")
        return None
    except Exception as e:
        logger.error(f"网页处理过程中发生错误: {e}")
        return None


def analyze_image_with_ai(image_path, purpose, config_path="config/config.yaml"):
    """使用AI模型分析图片内容"""
    try:
        # 添加项目根目录到Python路径（如果还没有添加的话）
        import sys

        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if project_root not in sys.path:
            sys.path.append(project_root)

        import base64

        from camel.messages import BaseMessage

        from utils.common import AgentFactory, Config

        # 加载配置和创建模型
        config = Config(config_path)
        model = AgentFactory.create_model(config)

        # 创建图片分析代理
        analyzer_agent = AgentFactory.create_analyzer_agent(
            model,
            "图片内容分析专家",
            "你是一个专业的图片内容分析专家，能够详细分析图片中的内容，包括文字、图表、图形、场景等，并生成结构化的markdown报告。",
        )

        # 读取并编码图片
        with open(image_path, "rb") as f:
            image_data = f.read()

        # 检查图片大小，如果过大则压缩
        image_size_mb = len(image_data) / (1024 * 1024)
        if image_size_mb > 10:  # 如果图片超过10MB，进行压缩
            logger.warning(f"图片文件较大 ({image_size_mb:.2f}MB)，尝试压缩...")
            try:
                import io

                from PIL import Image

                # 打开图片并压缩
                with Image.open(image_path) as img:
                    # 转换为RGB模式（如果是RGBA或其他模式）
                    if img.mode in ("RGBA", "LA", "P"):
                        img = img.convert("RGB")

                    # 计算压缩比例
                    max_dimension = 2048
                    if max(img.size) > max_dimension:
                        ratio = max_dimension / max(img.size)
                        new_size = (int(img.size[0] * ratio), int(img.size[1] * ratio))
                        img = img.resize(new_size, Image.Resampling.LANCZOS)

                    # 保存为JPEG格式并压缩
                    buffer = io.BytesIO()
                    img.save(buffer, format="JPEG", quality=85, optimize=True)
                    image_data = buffer.getvalue()

                logger.info(f"图片已压缩至 {len(image_data) / (1024 * 1024):.2f}MB")
            except ImportError:
                logger.warning("PIL库未安装，无法压缩图片，将使用原始图片")
            except Exception as e:
                logger.warning(f"图片压缩失败: {e}，将使用原始图片")

        # Base64编码
        image_base64 = base64.b64encode(image_data).decode("utf-8")

        # 获取图片文件名和扩展名
        filename = os.path.basename(image_path)
        file_ext = os.path.splitext(filename)[1].lower()

        # 构建分析提示
        analysis_prompt = f"""
请详细分析这张图片的内容，生成结构化的markdown报告。

**分析目标**: {purpose}

**分析要求**:
1. **整体描述**: 图片的主要内容和主题
2. **详细内容**:
   - 如果包含文字，请提取并整理所有可读文字
   - 如果包含图表、表格，请描述数据和结构
   - 如果包含流程图、架构图，请说明各部分的关系
   - 如果包含界面截图，请描述界面元素和功能
3. **技术信息**: 如果是技术相关图片，请提取技术要点
4. **关键见解**: 从图片中提取的重要信息和结论
5. **应用价值**: 这些内容在实际应用中的价值

**输出格式**:
```markdown
# 图片内容分析报告

## 基本信息
- **文件名**: {filename}
- **分析时间**: [当前时间]
- **图片类型**: [图片类型描述]

## 整体描述
[图片的主要内容和主题描述]

## 详细内容分析

### 文字内容
[如果有文字，请逐一提取并整理]

### 图表数据
[如果有图表、表格，请详细描述]

### 结构组织
[如果有流程图、架构图等，请描述结构关系]

### 界面元素
[如果是界面截图，请描述各功能模块]

## 技术要点
[提取的技术信息和要点]

## 关键见解
[从图片中得出的重要结论和见解]

## 应用价值
[这些内容的实际应用价值和意义]
```

请基于图片内容生成详细的分析报告，确保信息准确完整。
"""

        # 构建包含图片的消息
        message_content = [
            {"type": "text", "text": analysis_prompt},
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/{file_ext[1:] if file_ext else 'jpeg'};base64,{image_base64}",
                    "detail": "high",  # 使用高精度模式以获得更详细的分析
                },
            },
        ]

        # 调用模型分析
        logger.info(f"开始AI分析图片: {filename}")
        response = analyzer_agent.step(BaseMessage.make_user_message(role_name="User", content=message_content))

        analysis_result = response.msg.content
        logger.info(f"图片分析完成，生成内容长度: {len(analysis_result)} 字符")

        return analysis_result

    except Exception as e:
        logger.error(f"AI图片分析失败: {e}")
        # 返回基本的图片信息作为备用方案
        filename = os.path.basename(image_path)
        return f"""# 图片内容分析报告

## 基本信息
- **文件名**: {filename}
- **文件路径**: {image_path}
- **状态**: AI分析失败

## 错误信息
{str(e)}

## 建议
请检查以下内容：
1. 确保图片文件有效且未损坏
2. 检查AI模型配置是否正确
3. 确认模型是否支持视觉分析功能
4. 检查网络连接是否正常

## 手动分析
请手动查看图片内容并补充相关信息。
"""


def process_local_file_source(source_config, project_name, output_dir):
    """处理本地文件源"""
    source_path = source_config["path"]
    analysis_file = f"{output_dir}/local_file_analysis.md"

    if os.path.exists(analysis_file):
        logger.info(f"本地文件分析已存在: {analysis_file}，跳过步骤1")
        return analysis_file

    # 复制或处理本地文件
    try:
        import shutil

        # 检查是否为图片文件
        image_extensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".tif", ".svg", ".webp"]
        file_ext = os.path.splitext(source_path)[1].lower()

        if (
            source_config.get("file_type") in ["image", "png", "jpg", "jpeg", "gif", "bmp", "tiff", "svg", "webp"]
            or file_ext in image_extensions
        ):
            # 处理图片文件，使用AI分析内容
            logger.info(f"检测到图片文件，开始AI分析: {source_path}")
            try:
                # 获取用户目的用于指导分析
                purpose = source_config.get("purpose", "分析图片内容，提取关键信息")

                # 调用AI分析图片
                analysis_result = analyze_image_with_ai(source_path, purpose)

                # 保存分析结果
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write(analysis_result)

                logger.info(f"图片AI分析完成: {analysis_file}")
                logger.info(f"分析内容长度: {len(analysis_result)} 字符")

                # 复制原始图片到输出目录的media文件夹
                media_dir = os.path.join(output_dir, "media")
                os.makedirs(media_dir, exist_ok=True)

                original_filename = os.path.basename(source_path)
                target_image_path = os.path.join(media_dir, original_filename)
                shutil.copy2(source_path, target_image_path)
                logger.info(f"原始图片已复制到: {target_image_path}")

                # 在markdown中添加原始图片引用
                with open(analysis_file, "a", encoding="utf-8") as f:
                    f.write("\n\n## 原始图片\n\n")
                    f.write(f"![原始图片]({os.path.join('media', original_filename)})\n\n")
                    f.write(f"**图片路径**: `{target_image_path}`\n")

                return analysis_file

            except Exception as e:
                logger.error(f"图片AI分析失败: {e}")
                # 创建基本描述文件作为备用方案
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write("# 本地图片文件分析\n\n")
                    f.write(f"**文件路径:** {source_path}\n\n")
                    f.write(f"**文件类型:** 图片文件 ({file_ext})\n\n")
                    f.write("**状态:** AI分析失败\n\n")
                    f.write(f"**错误信息:** {str(e)}\n\n")
                    f.write("## 建议\n")
                    f.write("1. 请检查AI模型配置是否支持视觉分析\n")
                    f.write("2. 确认图片文件是否有效\n")
                    f.write("3. 手动查看图片并补充相关信息\n\n")

                    # 尝试复制原始图片
                    try:
                        media_dir = os.path.join(output_dir, "media")
                        os.makedirs(media_dir, exist_ok=True)
                        original_filename = os.path.basename(source_path)
                        target_image_path = os.path.join(media_dir, original_filename)
                        shutil.copy2(source_path, target_image_path)
                        f.write("## 原始图片\n\n")
                        f.write(f"![原始图片]({os.path.join('media', original_filename)})\n\n")
                        f.write(f"**图片路径**: `{target_image_path}`\n")
                    except Exception as copy_e:
                        logger.warning(f"复制图片失败: {copy_e}")
                        f.write(f"**原始图片路径**: {source_path}\n")

                return analysis_file

        elif source_config.get("file_type") == "markdown" or source_path.endswith(".md"):
            shutil.copy2(source_path, analysis_file)
        elif source_config.get("file_type") in ["docx", "pptx", "xlsx", "xls"] or source_path.endswith(
            (".docx", ".pptx", ".xlsx", ".xls", ".ppt", ".doc")
        ):
            # 使用微软MarkItDown工具处理Office文件和其他支持格式
            file_type = source_config.get("file_type", "office")
            if source_path.endswith(".docx"):
                file_type = "DOCX"
            elif source_path.endswith(".pptx") or source_path.endswith(".ppt"):
                file_type = "PowerPoint"
            elif source_path.endswith(".xlsx") or source_path.endswith(".xls"):
                file_type = "Excel"
            elif source_path.endswith(".doc"):
                file_type = "Word"

            logger.info(f"检测到{file_type}文件，使用MarkItDown转换: {source_path}")
            try:
                from markitdown import MarkItDown

                # 初始化MarkItDown转换器
                md_converter = MarkItDown()

                # 转换文件
                logger.info("开始使用MarkItDown转换文件...")
                result = md_converter.convert(source_path)

                if not result or not result.text_content:
                    logger.error("MarkItDown转换返回空内容")
                    raise Exception("转换结果为空")

                # 直接写入markdown文件
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write(result.text_content)

                logger.info(f"{file_type}文件转换完成: {analysis_file}")
                logger.info(f"转换内容长度: {len(result.text_content)} 字符")

                return analysis_file

            except ImportError as e:
                logger.error(f"无法导入MarkItDown库: {e}")
                logger.error("请安装MarkItDown库: pip install 'markitdown[all]'")
                return None
            except Exception as e:
                logger.error(f"{file_type}文件转换失败: {e}")
                return None
        elif source_config.get("file_type") == "pdf" or source_path.endswith(".pdf"):
            # 处理PDF文件，使用PDFToolkit解析为markdown
            logger.info(f"检测到PDF文件，开始解析: {source_path}")
            try:
                from tools.pdf_toolkit import PDFToolkit

                # 初始化PDF工具包
                pdf_toolkit = PDFToolkit()

                # 确保PDFToolkit使用与工作流相同的项目名
                base_output_dir = os.path.dirname(output_dir)  # 获取output目录

                # 使用PDFToolkit提取PDF内容，但指定项目名
                result = pdf_toolkit.extract_pdf(source_path, base_output_dir)

                if "error" in result:
                    logger.error(f"PDF解析失败: {result['error']}")
                    # 创建错误提示文件
                    with open(analysis_file, "w", encoding="utf-8") as f:
                        f.write("# 本地PDF文件分析\n\n")
                        f.write(f"**文件路径:** {source_path}\n\n")
                        f.write("**状态:** 解析失败\n\n")
                        f.write(f"**错误信息:** {result['error']}\n\n")
                        f.write("请检查PDF文件是否有效或者是否存在权限问题。\n")
                    return analysis_file

                # 检查生成的markdown文件
                generated_markdown = result.get("markdown_file")
                if not generated_markdown or not os.path.exists(generated_markdown):
                    logger.error(f"生成的markdown文件不存在: {generated_markdown}")
                    return None

                # 复制或移动文件到最终分析文件位置
                if os.path.abspath(generated_markdown) != os.path.abspath(analysis_file):
                    shutil.copy2(generated_markdown, analysis_file)
                    logger.info(f"已将PDF解析结果从 {generated_markdown} 复制到 {analysis_file}")
                else:
                    logger.info(f"生成的文件已经在目标位置: {analysis_file}")

                return analysis_file

            except ImportError as e:
                logger.error(f"无法导入PDFToolkit: {e}")
                # 创建基本描述文件作为备用方案
                with open(analysis_file, "w", encoding="utf-8") as f:
                    f.write("# 本地PDF文件分析\n\n")
                    f.write(f"**文件路径:** {source_path}\n\n")
                    f.write("**文件类型:** PDF\n\n")
                    f.write("**状态:** PDF解析工具不可用\n\n")
                    f.write(f"**错误信息:** {str(e)}\n\n")
                    f.write("请安装PDF解析依赖或检查工具配置。\n")
                return analysis_file
        else:
            # 对于其他文件类型，创建一个描述文件
            with open(analysis_file, "w", encoding="utf-8") as f:
                f.write(f"# 本地文件分析\n\n文件路径: {source_path}\n\n")
                f.write(f"文件类型: {source_config.get('file_type', 'unknown')}\n\n")
                f.write("注意：当前支持以下文件格式的详细处理：\n")
                f.write("- Markdown (.md)\n")
                f.write("- PDF (.pdf)\n")
                f.write("- Microsoft Office文件 (.docx, .pptx, .xlsx, .xls, .doc, .ppt) - 通过MarkItDown\n")
                f.write("- 图片文件 (.png, .jpg, .jpeg, .gif, .bmp, .tiff, .svg, .webp) - 通过AI视觉分析\n")
                f.write("- 其他MarkItDown支持的格式\n\n")

        logger.info(f"本地文件处理完成: {analysis_file}")
        return analysis_file
    except Exception as e:
        logger.error(f"本地文件处理失败: {e}")
        return None


def process_chat_source(source_config, project_name, output_dir):
    """处理chat源 - 不生成中间文件，返回特殊标识让后续流程知道这是chat模式"""
    purpose = source_config.get("purpose", "")
    if not purpose:
        logger.error("Chat源配置缺少purpose字段")
        return None

    logger.info(f"Chat源检测到主题: {purpose}")
    logger.info("Chat模式将跳过中间文件生成，直接进入material_agent_refactored流程")

    # 返回特殊标识，告诉调用者这是chat模式
    return "CHAT_MODE_DIRECT"


def process_material_source(source_config, project_name, output_dir):
    """根据材料源类型处理内容"""
    source_type = source_config["type"]

    if source_type == "github":
        return process_github_source(source_config, project_name, output_dir)
    elif source_type == "pdf":
        return process_pdf_source(source_config, project_name, output_dir)
    elif source_type == "webpage":
        return process_webpage_source(source_config, project_name, output_dir)
    elif source_type == "local_file":
        return process_local_file_source(source_config, project_name, output_dir)
    elif source_type == "chat":
        return process_chat_source(source_config, project_name, output_dir)
    else:
        logger.error(f"不支持的材料源类型: {source_type}")
        return None


def build_enhancement_config(args):
    """构建素材扩充配置 - 仅作为命令行覆盖用"""
    # 只有明确指定命令行参数时才返回覆盖配置
    if args.enable_video or args.disable_video or args.enable_image or args.enable_audio:
        return {
            "video_recording": (args.enable_video or not args.disable_video) and not args.disable_video,
            "image_generation": args.enable_image,
            "audio_synthesis": args.enable_audio,
        }
    return None  # 使用config默认配置


def run_chat_mode(args):
    """
    运行chat模式：用户输入主题，直接通过material_agent_refactored生成素材，然后生成视频
    """
    logger.info("===== Chat模式启动 =====")

    # 加载配置以获取chat设置
    config = load_config(args.config)
    material_config = config.get("material", {})
    sources_config = material_config.get("sources", {})
    chat_config = sources_config.get("chat", {})

    # 获取用户输入的主题
    if args.purpose:
        purpose = args.purpose
        logger.info(f"使用命令行参数中的主题: {purpose}")
    else:
        # 尝试从配置文件获取主题
        config_purpose = chat_config.get("purpose", "")
        if config_purpose:
            purpose = config_purpose
            logger.info(f"使用配置文件中的主题: {purpose}")
        else:
            # 交互式获取主题
            print("\n欢迎使用Chat模式！")
            print("请输入您想了解的主题，我将为您生成详细的解释视频。")
            print("例如：深度学习、区块链技术、量子计算等")
            purpose = input("\n请输入主题: ").strip()

            if not purpose:
                logger.error("未输入有效主题，退出chat模式")
                return

    logger.info(f"开始处理主题: {purpose}")

    # 生成项目名称（使用时间戳）
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    project_name = f"chat_{timestamp}"

    logger.info(f"生成项目名称: {project_name}")

    # 创建输出目录
    output_dir = ensure_output_directory(project_name)

    # 构建默认的purpose描述（用于视频生成）
    video_purpose = f"{purpose}"

    # 构建素材扩充配置
    enhancement_config = build_enhancement_config(args)

    # 显示启用的扩充功能（从config读取）
    config_material = config.get("material", {})
    material_enhance = config_material.get("material_enhance", {})

    enabled_features = []
    # 优先显示命令行覆盖的配置，其次显示config配置
    if enhancement_config:
        if enhancement_config.get("video_recording"):
            enabled_features.append("录屏视频")
        if enhancement_config.get("image_generation"):
            enabled_features.append("图片生成")
        if enhancement_config.get("audio_synthesis"):
            enabled_features.append("音频合成")
    else:
        if material_enhance.get("screen_record"):
            enabled_features.append("录屏视频")
        if material_enhance.get("image_generation"):
            enabled_features.append("图片生成")
        if material_enhance.get("audio_synthesis"):
            enabled_features.append("音频合成")

    if enabled_features:
        logger.info(f"🎬 已启用扩充: {', '.join(enabled_features)}")
    else:
        logger.info("🎬 未启用任何扩充功能")

    # 步骤1: 直接使用material_agent_refactored生成项目介绍素材（Chat模式）
    intro_md_path = f"{output_dir}/{project_name}_intro.md"
    if os.path.exists(intro_md_path):
        logger.info(f"项目介绍文件已存在: {intro_md_path}，跳过步骤1")
    else:
        # 构建扩充选项 - 安全访问enhancement_config
        enhancement_options = []
        if enhancement_config and enhancement_config.get("video_recording"):
            enhancement_options.append("--enable-video")
        if enhancement_config and enhancement_config.get("image_generation"):
            enhancement_options.append("--enable-image")
        if enhancement_config and enhancement_config.get("audio_synthesis"):
            enhancement_options.append("--enable-audio")
        if args.disable_video:
            enhancement_options.append("--disable-video")

        enhancement_str = " ".join(enhancement_options)

        # Chat模式：不提供材料文件，让agent知道这是chat模式
        step1_cmd = (
            f"python -m agents.material_agent_refactored "
            f'--purpose "{video_purpose}" '
            f"--output {intro_md_path} "
            f"{enhancement_str}"
        )

        if not run_command(step1_cmd, "步骤1: Chat模式生成项目介绍素材"):
            logger.error("步骤1失败，终止chat模式工作流")
            return

        if not os.path.exists(intro_md_path):
            logger.error(f"项目介绍文件未生成: {intro_md_path}")
            return

    logger.info(f"素材内容已生成: {intro_md_path}")

    # 步骤2: 生成动画脚本故事板
    storyboard_path = f"{output_dir}/{project_name}_intro_storyboard.json"
    if os.path.exists(storyboard_path):
        logger.info(f"故事板文件已存在: {storyboard_path}，跳过步骤2")
    else:
        step2_cmd = (
            f"python -m agents.generate_manim_dsl_agent_refactored "
            f"--markdown {intro_md_path} "
            f'--purpose "{purpose}" '
            f"--output {storyboard_path}"
        )

        if not run_command(step2_cmd, "步骤2: 生成动画脚本故事板"):
            logger.error("步骤2失败，终止工作流")
            return

        if not os.path.exists(storyboard_path):
            logger.error(f"故事板文件未生成: {storyboard_path}")
            return

    # 步骤3: 渲染最终视频
    video_output_dir = f"output/{project_name}/videos"
    if os.path.exists(video_output_dir) and len(os.listdir(video_output_dir)) > 0:
        logger.info(f"视频文件目录已存在且不为空: {video_output_dir}，跳过步骤3")
    else:
        step3_cmd = f"sh run_storyboards.sh -f {storyboard_path} -q h --project-name {project_name}"
        if not run_command(step3_cmd, "步骤3: 渲染最终视频"):
            logger.error("步骤3失败")
            return

    logger.info("Chat模式工作流成功完成！")
    print("\n🎉 视频生成完成！")
    print(f"📁 输出目录: {output_dir}")
    print(f"📄 素材内容: {intro_md_path}")
    if os.path.exists(video_output_dir):
        print(f"🎬 视频文件: {video_output_dir}")
    else:
        print("❌ 视频生成失败，请检查日志文件")


def run_common_workflow(project_name, analysis_file, purpose, output_dir, enhancement_config):
    """执行通用工作流步骤（生成素材、故事板、视频）- 使用重构后的代理"""

    # 主线步骤二: 结合输入材料生成项目讲解素材，使用重构后的material_agent
    intro_md_path = f"{output_dir}/{project_name}_intro.md"
    if os.path.exists(intro_md_path):
        logger.info(f"项目介绍文件已存在: {intro_md_path}，跳过步骤2")
    else:
        # 构建扩充选项（仅在有覆盖配置时添加）
        enhancement_str = ""
        if enhancement_config:
            enhancement_options = []
            if enhancement_config.get("video_recording"):
                enhancement_options.append("--enable-video")
            if enhancement_config.get("image_generation"):
                enhancement_options.append("--enable-image")
            if enhancement_config.get("audio_synthesis"):
                enhancement_options.append("--enable-audio")
            if not enhancement_config.get("video_recording"):
                enhancement_options.append("--disable-video")
            enhancement_str = " ".join(enhancement_options)

        step2_cmd = (
            f"python -m agents.material_agent_refactored "
            f"--material {analysis_file} "
            f'--purpose "{purpose}" '
            f"--output {intro_md_path} "
            f"{enhancement_str}"
        ).strip()

        if not run_command(step2_cmd, "步骤2: 使用重构代理生成项目介绍素材"):
            logger.error("步骤2失败，终止工作流")
            return False

        if not os.path.exists(intro_md_path):
            logger.error(f"项目介绍文件未生成: {intro_md_path}")
            return False

    # 主线步骤三: 生成动画分镜故事板storyboard - 使用重构后的DSL代理
    storyboard_path = f"{output_dir}/{project_name}_intro_storyboard.json"
    if os.path.exists(storyboard_path):
        logger.info(f"故事板文件已存在: {storyboard_path}，跳过步骤3")
    else:
        step3_cmd = (
            f"python -m agents.generate_manim_dsl_agent_refactored "
            f"--markdown {intro_md_path} "
            f'--purpose "{purpose}" '
            f"--output {storyboard_path}"
        )

        if not run_command(step3_cmd, "步骤3: 使用重构代理生成动画脚本故事板"):
            logger.error("步骤3失败，终止工作流")
            return False

        if not os.path.exists(storyboard_path):
            logger.error(f"故事板文件未生成: {storyboard_path}")
            return False

    # 主线步骤四: 用manim渲染最终视频
    video_output_dir = f"output/{project_name}/videos"
    if os.path.exists(video_output_dir) and len(os.listdir(video_output_dir)) > 0:
        logger.info(f"视频文件目录已存在且不为空: {video_output_dir}，跳过步骤4")
    else:
        step4_cmd = f"sh run_storyboards.sh -f {storyboard_path} -q h --project-name {project_name}"
        if not run_command(step4_cmd, "步骤4: 渲染最终视频"):
            logger.error("步骤4失败")
            return False

    logger.info("工作流成功完成！")
    logger.info(f"输出视频应该位于: {video_output_dir}")
    return True


def main():
    """主函数，执行完整工作流"""
    args = parse_args()

    # 检查是否为chat模式（通过--chat或--source chat）
    if args.chat or args.source == "chat":
        logger.info("启动chat模式")
        run_chat_mode(args)
        return

    # 加载配置
    config = load_config(args.config)

    # 获取活跃的材料源
    source_config = get_active_material_source(config, args.source)
    logger.info(f"使用材料源: {source_config['type']}")

    # 验证材料源配置
    if not validate_source_config(source_config):
        logger.error("材料源配置验证失败")
        sys.exit(1)

    # 提取项目名
    project_name = extract_project_name(source_config)
    logger.info(f"项目名称: {project_name}")

    # 创建输出目录
    output_dir = ensure_output_directory(project_name)

    # 获取视频目的
    purpose = get_purpose_from_config(config, source_config, args.purpose)
    logger.info(f"视频目的: {purpose}")

    # 构建素材扩充配置
    enhancement_config = build_enhancement_config(args)

    # 显示启用的扩充功能（从config读取）
    config_material = config.get("material", {})
    material_enhance = config_material.get("material_enhance", {})

    enabled_features = []
    # 优先显示命令行覆盖的配置，其次显示config配置
    if enhancement_config:
        if enhancement_config.get("video_recording"):
            enabled_features.append("录屏视频")
        if enhancement_config.get("image_generation"):
            enabled_features.append("图片生成")
        if enhancement_config.get("audio_synthesis"):
            enabled_features.append("音频合成")
    else:
        if material_enhance.get("screen_record"):
            enabled_features.append("录屏视频")
        if material_enhance.get("image_generation"):
            enabled_features.append("图片生成")
        if material_enhance.get("audio_synthesis"):
            enabled_features.append("音频合成")

    if enabled_features:
        logger.info(f"🎬 已启用扩充: {', '.join(enabled_features)}")
    else:
        logger.info("🎬 未启用任何扩充功能")

    # 主线步骤一：处理材料源生成md
    analysis_file = process_material_source(source_config, project_name, output_dir)
    if not analysis_file:
        logger.error("材料源处理失败，终止工作流")
        sys.exit(1)

    # 检查是否为chat源的特殊返回值
    if analysis_file == "CHAT_MODE_DIRECT":
        logger.info("检测到Chat源，切换到Chat模式流程")
        # 获取chat配置中的purpose
        chat_purpose = source_config.get("purpose", "")
        if not chat_purpose:
            logger.error("Chat源配置缺少purpose字段")
            sys.exit(1)

        # 构建完整的视频purpose描述
        video_purpose = f"{chat_purpose}"

        # 构建扩充选项
        enhancement_options = []
        if enhancement_config and enhancement_config.get("video_recording"):
            enhancement_options.append("--enable-video")
        if enhancement_config and enhancement_config.get("image_generation"):
            enhancement_options.append("--enable-image")
        if enhancement_config and enhancement_config.get("audio_synthesis"):
            enhancement_options.append("--enable-audio")
        if args.disable_video:
            enhancement_options.append("--disable-video")

        enhancement_str = " ".join(enhancement_options)

        # 步骤1: 直接使用material_agent_refactored生成项目介绍素材（Chat模式）
        intro_md_path = f"{output_dir}/{project_name}_intro.md"
        if os.path.exists(intro_md_path):
            logger.info(f"项目介绍文件已存在: {intro_md_path}，跳过步骤1")
        else:
            step1_cmd = (
                f"python -m agents.material_agent_refactored "
                f'--purpose "{video_purpose}" '
                f"--output {intro_md_path} "
                f"{enhancement_str}"
            )

            if not run_command(step1_cmd, "步骤1: Chat模式生成项目介绍素材"):
                logger.error("步骤1失败，终止工作流")
                sys.exit(1)

            if not os.path.exists(intro_md_path):
                logger.error(f"项目介绍文件未生成: {intro_md_path}")
                sys.exit(1)

        # 将intro_md_path设为analysis_file，这样就可以继续使用通用工作流
        analysis_file = intro_md_path
        purpose = video_purpose

    # 非chat模式直接执行后续通用步骤
    success = run_common_workflow(project_name, analysis_file, purpose, output_dir, enhancement_config)

    if success:
        logger.info("所有步骤执行完成！")
        print("\n🎉 视频生成成功完成！")
        print(f"📁 项目目录: output/{project_name}")
        print(f"📄 素材文件: {analysis_file}")
        print(f"🎬 视频目录: output/{project_name}/videos")

        # 显示使用的扩充功能
        if enabled_features:
            print(f"✨ 扩充功能: {', '.join(enabled_features)}")
    else:
        logger.error("工作流执行失败")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.exception(f"工作流执行过程中发生错误: {str(e)}")
        sys.exit(1)
