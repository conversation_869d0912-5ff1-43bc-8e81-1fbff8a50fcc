<SCENE_VISION_STORYBOARD>
场景1：BPE Algorithm - 魔法积木造词记

数据结构定义：
- 标题组Group：Manim VGroup，包含主标题"BPE Algorithm"、副标题"Byte Pair Encoding: 魔法积木造词记"和装饰线。
- 布局区域Group：三个Rectangle区域，左侧词汇表区域、右侧操作说明区域、中间主要内容区域。
- 原始语料Group：Manim Text Mobject集合，包含两行文本"你好棒你真棒"和"你好酷你好棒"。
- 字符块Group：Manim VGroup集合，每个字符用圆角矩形背景的词块表示，分为两个序列。
- 词汇表Group：Manim VGroup，包含表格标题和数据行，显示字符及其频率。结构：表头Rectangle + 数据行Rectangle。
- 操作说明Group：Manim VGroup，包含步骤标题和描述文本，动态更新内容。
- 合并对Group：临时高亮的字符对，用于标识将要合并的字符。
- 新词块Group：合并后生成的新词块，具有不同的颜色标识。
- 编码文本Group：Manim Text Mobject，展示待编码的新句子。
- 编码结果Group：Manim VGroup，展示最终的编码令牌序列。

布局区域规划：
- 标题区域(TOP)：屏幕上方，用于显示场景标题。约占屏幕高度的15%。
- 左侧词汇表区域(LEFT)：宽度3单位，高度6单位，显示词汇表。位置：to_edge(LEFT, buff=0.5)。
- 右侧操作说明区域(RIGHT)：宽度3单位，高度6单位，显示当前步骤说明。位置：to_edge(RIGHT, buff=0.5)。
- 中间主要内容区域(CENTER)：宽度6单位，高度6单位，显示核心动画内容。位置：ORIGIN。
- 背景设置：纯黑色背景(BLACK)，无框线显示。

阶段1：封面动画
- 标题组Group：主标题字体大小72pt，副标题36pt，装饰线条橙色。
- 核心动作：
    - `Write(主标题, run_time=2, rate_func=smooth)`：书写主标题"BPE Algorithm"。
    - `FadeIn(副标题, shift=UP) + Create(装饰线, run_time=1.5)`：副标题从下方淡入，装饰线同时创建。
    - `Wait(2)`：停留2秒展示完整标题。
    - `FadeOut(标题组Group, run_time=1)`：标题组淡出，为后续内容让位。
- 布局关系：标题组居中显示，垂直排列，间距0.5单位。

阶段2：布局初始化
- 布局区域Group：三个区域标题，不显示框线。
- 核心动作：
    - `Write(词汇表标题) + Write(操作说明标题) + Write(主要内容标题)`：同时显示三个区域的标题。
    - `Wait(1)`：停留1秒确认布局。
- 布局关系：词汇表标题在左上方，操作说明标题在右上方，主要内容标题在中上方。

阶段3：步骤1 - 初始化词表
- 原始语料Group：两行文本，字体大小36pt，橙色(ACCENT)。
- 操作说明Group：步骤标题"步骤1：初始化"，描述文本"将文本拆分为单个字符\n统计每个字符的频率"。
- 核心动作：
    - `Write(语料标题) + Write(原始语料Group, run_time=2)`：显示原始语料标题和内容。
    - `ReplacementTransform(原始语料Group -> 字符块Group)`：将文本转换为字符块，每个字符用圆角矩形包装。
    - `FadeIn(词汇表Group, shift=RIGHT, run_time=1.5)`：词汇表从右侧滑入。
    - `FadeIn(操作说明Group, shift=LEFT, run_time=1)`：操作说明从左侧滑入。
- 布局关系：原始语料在中央上方，字符块在中央中部，词汇表在左侧，操作说明在右侧。
- 数据内容：词汇表显示字符频率 - 你:4, 好:3, 棒:3, 真:1, 酷:1。

阶段4：步骤2 - 第一次合并
- 操作说明Group：更新为"步骤2：第一次合并 - 找到最高频的字符对'你好' 出现3次，将其合并为一个子词"。
- 合并动画Group：高亮"你好"字符对，执行合并动画。
- 核心动作：
    - `ReplacementTransform(操作说明Group -> 新操作说明Group)`：更新操作说明内容。
    - `perform_merge_animation("你", "好", "你好", SUCCESS色)`：执行合并动画，包含以下子步骤：
        - 找到所有"你好"字符对（3个）
        - 从后往前排序执行合并
        - `move_to(新块中心位置)` -> `ReplacementTransform(VGroup(block1, block2) -> 新块)`
    - `ReplacementTransform(词汇表Group -> 新词汇表Group)`：更新词汇表，高亮新词"你好"。
- 布局关系：合并动画在中央区域执行，词汇表在左侧更新，操作说明在右侧更新。
- 数据内容：更新后词汇表 - 你:1, 好:0, 棒:3, 真:1, 酷:1, 你好:3。

阶段5：步骤3 - 第二次合并
- 操作说明Group：更新为"步骤3：第二次合并 - 找到最高频的字符对'你好棒' 出现2次，将其合并为一个子词"。
- 合并动画Group：高亮"你好棒"字符对，执行合并动画。
- 核心动作：
    - `ReplacementTransform(操作说明Group -> 新操作说明Group)`：更新操作说明。
    - `perform_merge_animation("你好", "棒", "你好棒", WARNING色)`：执行第二次合并动画。
    - `ReplacementTransform(词汇表Group -> 新词汇表Group)`：更新词汇表，高亮新词"你好棒"。
- 布局关系：合并动画在中央区域执行，词汇表在左侧更新。
- 数据内容：更新后词汇表 - 你:1, 好:0, 棒:1, 真:1, 酷:1, 你好:1, 你好棒:2。

阶段6：步骤4 - 停止条件
- 操作说明Group：更新为"步骤4：达到停止条件 - 预设合并次数：2次，算法停止，最终词表包含7个子词"。
- 最终词汇表Group：完整的词汇表，用高亮框包围。
- 核心动作：
    - `ReplacementTransform(操作说明Group -> 新操作说明Group)`：更新操作说明。
    - `ReplacementTransform(词汇表Group -> 最终词汇表Group)`：更新为最终词汇表。
    - `Create(SurroundingRectangle(最终词汇表Group, color=SUCCESS, stroke_width=3))`：用绿色框高亮最终词汇表。
- 布局关系：最终词汇表在左侧，带有绿色高亮框。
- 数据内容：最终词汇表 - 你:1, 好:0, 棒:1, 真:1, 酷:1, 你好:1, 你好棒:2。

阶段7：步骤5 - 实际应用
- 操作说明Group：更新为"步骤5：实际应用 - 使用学到的子词词表对新句子进行编码，从最长子词开始匹配"。
- 编码文本Group：新句子"你好棒真棒"，字体大小36pt。
- 编码结果Group：最终编码令牌序列。
- 核心动作：
    - `FadeOut(中央区域所有元素)`：清理中央区域。
    - `Write(句子标题) + Write(编码文本Group)`：显示待编码句子。
    - `ReplacementTransform(编码文本Group -> 字符块Group)`：将句子转换为字符块。
    - `animate_encoding_step(字符块Group, 0, 3, "你好棒")`：执行编码动画，匹配"你好棒"。
    - `FadeIn(剩余令牌说明)`：显示剩余字符的说明。
    - `FadeIn(编码结果Group)`：显示最终编码结果。
- 布局关系：编码过程在中央区域执行，词汇表在左侧作为参考。
- 数据内容：编码结果 - ["你好棒", "真", "棒"]，每个令牌用不同颜色区分。

词块设计规范：
- 词块背景：RoundedRectangle(corner_radius=0.1, fill_opacity=0.8, stroke_width=2)
- 词块宽度：动态计算 - base_width(0.5) + len(text) * 0.35
- 词块高度：固定0.7单位
- 词块颜色：PRIMARY(深蓝)、SECONDARY(深紫红)、SUCCESS(绿色)、WARNING(黄色)等
- 文字：字体大小24pt，白色，粗体

词汇表设计规范：
- 表头：Rectangle(width=2.8, height=0.4, fill_color=ACCENT, fill_opacity=0.8)
- 数据行：Rectangle(width=2.8, height=0.4, fill_color=SURFACE, fill_opacity=0.3)
- 高亮行：Rectangle(fill_color=SUCCESS, fill_opacity=0.6) 用于新词
- 文字：表头16pt白色粗体，数据行14pt主要文本色

动画设计说明：
- 核心Transform动作：
    1. `Write`: 用于文字书写效果，如标题和说明文本
    2. `FadeIn/FadeOut`: 用于元素的淡入淡出，常配合shift参数
    3. `ReplacementTransform`: 用于元素的完全替换，如语料转字符块
    4. `Create`: 用于几何图形的创建，如装饰线和高亮框
    5. `animate.move_to`: 用于元素的位置移动动画
- 时间控制：大多数动画run_time=1-2秒，重要转换2-2.5秒
- 速率函数：主要使用smooth平滑函数
- 颜色主题：ModernColors配色方案，深色背景+亮色文字+彩色强调
- 布局策略：三栏布局，固定区域分配，动态内容更新
- 错误处理：合并动画中添加索引范围检查，防止越界错误

技术实现要点：
- 背景设置：self.camera.background_color = BLACK
- 区域定义：使用Rectangle定义区域但不显示框线(stroke_width=0)
- 序列管理：self.current_sequences列表管理当前字符块状态
- 合并逻辑：从后往前排序执行合并，避免索引变化问题
- 动态宽度：词块宽度根据字符数量动态调整
- 布局响应：使用to_edge、next_to等进行相对定位 