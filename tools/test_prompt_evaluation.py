#!/usr/bin/env python3
"""
测试Prompt评估工具的示例脚本
"""

import sys
import os
from pathlib import Path

# 添加父目录到导入路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.prompt_evaluation_tool import PromptEvaluationTool


def test_basic_evaluation():
    """测试基本的评估功能"""
    print("🧪 测试基本评估功能...")
    
    tool = PromptEvaluationTool()
    
    # 测试参数
    topic = "注意力机制"
    purpose = "帮助机器学习初学者理解注意力机制的核心原理"
    audience = "有基础编程经验但对深度学习了解有限的开发者"
    
    # 运行评估
    result = tool.run_evaluation(topic, purpose, audience)
    
    # 打印结果
    tool.print_evaluation_summary(result)
    
    return result


def test_multiple_topics():
    """测试多个主题的评估"""
    print("🧪 测试多个主题评估...")
    
    tool = PromptEvaluationTool()
    
    test_cases = [
        {
            "topic": "Transformer架构",
            "purpose": "向AI研究者介绍Transformer的核心创新点",
            "audience": "有深度学习基础的研究人员"
        },
        {
            "topic": "梯度下降算法",
            "purpose": "帮助数学专业学生理解优化算法的本质",
            "audience": "数学专业本科生"
        },
        {
            "topic": "卷积神经网络",
            "purpose": "向计算机视觉工程师解释CNN的工作原理",
            "audience": "有编程经验的计算机视觉工程师"
        }
    ]
    
    results = []
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试案例 {i}/{len(test_cases)}: {case['topic']}")
        print(f"{'='*60}")
        
        result = tool.run_evaluation(
            case["topic"], 
            case["purpose"], 
            case["audience"]
        )
        
        tool.print_evaluation_summary(result)
        results.append(result)
    
    return results


def test_custom_prompt():
    """测试使用自定义prompt文件"""
    print("🧪 测试自定义prompt文件...")
    
    # 检查prompt文件是否存在
    prompt_file = "prompts/example_gen_prompt.py"
    if not Path(prompt_file).exists():
        print(f"❌ Prompt文件不存在: {prompt_file}")
        return None
    
    tool = PromptEvaluationTool()
    
    # 测试参数
    topic = "BERT模型"
    purpose = "向NLP工程师介绍BERT的预训练机制"
    audience = "有自然语言处理经验的工程师"
    
    # 运行评估
    result = tool.run_evaluation(topic, purpose, audience, prompt_file)
    
    # 打印结果
    tool.print_evaluation_summary(result)
    
    return result


def analyze_evaluation_trends():
    """分析评估结果的趋势"""
    print("📊 分析评估结果趋势...")
    
    # 运行多个测试
    results = test_multiple_topics()
    
    if not results or any("error" in result for result in results):
        print("❌ 无法分析趋势，存在评估错误")
        return
    
    print("\n" + "="*80)
    print("📈 评估结果趋势分析")
    print("="*80)
    
    # 收集分数数据
    scores = []
    topics = []
    
    for result in results:
        if "evaluation" in result and "overall_score" in result["evaluation"]:
            scores.append(result["evaluation"]["overall_score"])
            topics.append(result["metadata"]["topic"])
    
    if scores:
        avg_score = sum(scores) / len(scores)
        max_score = max(scores)
        min_score = min(scores)
        
        print(f"📊 总体统计:")
        print(f"  • 平均分: {avg_score:.1f}")
        print(f"  • 最高分: {max_score} ({topics[scores.index(max_score)]})")
        print(f"  • 最低分: {min_score} ({topics[scores.index(min_score)]})")
        print(f"  • 分数范围: {max_score - min_score:.1f}")
        
        # 分析各维度表现
        dimension_scores = {}
        for result in results:
            if "evaluation" in result and "detailed_scores" in result["evaluation"]:
                for dim, details in result["evaluation"]["detailed_scores"].items():
                    if dim not in dimension_scores:
                        dimension_scores[dim] = []
                    dimension_scores[dim].append(details.get("score", 0))
        
        if dimension_scores:
            print(f"\n📈 各维度平均表现:")
            dimension_names = {
                "analogical_thinking_quality": "同构类比质量",
                "instructional_design_completeness": "教学设计完整性",
                "boundary_awareness": "边界意识与适用性",
                "step_summary_operability": "步骤总结可操作性",
                "visualization_quality": "视觉化描述质量"
            }
            
            for dim, scores_list in dimension_scores.items():
                avg = sum(scores_list) / len(scores_list)
                dim_name = dimension_names.get(dim, dim)
                print(f"  • {dim_name}: {avg:.1f}")
    
    print("="*80)


def main():
    """主函数"""
    print("🚀 Prompt评估工具测试")
    print("="*80)
    
    try:
        # 基本功能测试
        print("\n1️⃣ 基本功能测试")
        test_basic_evaluation()
        
        # 多主题测试
        print("\n2️⃣ 多主题测试")
        test_multiple_topics()
        
        # 自定义prompt测试
        print("\n3️⃣ 自定义prompt测试")
        test_custom_prompt()
        
        # 趋势分析
        print("\n4️⃣ 趋势分析")
        analyze_evaluation_trends()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
