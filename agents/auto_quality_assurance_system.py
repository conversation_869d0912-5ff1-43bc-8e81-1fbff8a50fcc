#!/usr/bin/env python3
"""
全自动教学视频质量保证系统
实现多层验证、自动修正和质量评估
"""

import json
import sympy as sp
import ast
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import subprocess
import tempfile

@dataclass
class QualityScore:
    """质量评分结构"""
    mathematical_accuracy: float  # 数学准确性 (0-1)
    logical_consistency: float    # 逻辑一致性 (0-1)
    code_validity: float         # 代码有效性 (0-1)
    teaching_effectiveness: float # 教学效果 (0-1)
    visual_clarity: float        # 视觉清晰度 (0-1)
    
    @property
    def overall_score(self) -> float:
        """计算总体质量分数"""
        weights = [0.25, 0.2, 0.2, 0.2, 0.15]  # 各维度权重
        scores = [self.mathematical_accuracy, self.logical_consistency, 
                 self.code_validity, self.teaching_effectiveness, self.visual_clarity]
        return sum(w * s for w, s in zip(weights, scores))

class MathematicalValidator:
    """数学内容验证器"""
    
    def __init__(self):
        self.known_theorems = {
            "勾股定理": {
                "formula": "a**2 + b**2 == c**2",
                "constraints": ["a > 0", "b > 0", "c > 0"],
                "geometric_rules": [
                    "直角三角形的斜边是最长边",
                    "三角形面积 = 0.5 * a * b",
                    "四个相同直角三角形可以拼成边长为(a+b)的正方形"
                ]
            }
        }
    
    def validate_mathematical_content(self, content: str, topic: str) -> Tuple[bool, float, List[str]]:
        """验证数学内容的正确性"""
        errors = []
        score = 1.0
        
        # 检查基本数学公式
        if topic in self.known_theorems:
            theorem = self.known_theorems[topic]
            
            # 验证公式是否正确出现
            if not self._check_formula_presence(content, theorem["formula"]):
                errors.append(f"缺少或错误的核心公式: {theorem['formula']}")
                score -= 0.3
            
            # 检查几何逻辑错误
            geometric_errors = self._check_geometric_logic(content, topic)
            if geometric_errors:
                errors.extend(geometric_errors)
                score -= 0.4
        
        # 检查数值计算
        calculation_errors = self._verify_calculations(content)
        if calculation_errors:
            errors.extend(calculation_errors)
            score -= 0.3
        
        is_valid = score >= 0.7
        return is_valid, max(0, score), errors
    
    def _check_formula_presence(self, content: str, formula: str) -> bool:
        """检查公式是否正确出现"""
        # 简化的公式检查
        formula_patterns = [
            r"a\^?2\s*\+\s*b\^?2\s*=\s*c\^?2",
            r"a²\s*\+\s*b²\s*=\s*c²",
            r"3\^?2\s*\+\s*4\^?2\s*=\s*5\^?2"
        ]
        
        for pattern in formula_patterns:
            if re.search(pattern, content):
                return True
        return False
    
    def _check_geometric_logic(self, content: str, topic: str) -> List[str]:
        """检查几何逻辑错误"""
        errors = []
        
        if topic == "勾股定理":
            # 检查常见的几何逻辑错误
            if "重排" in content and "三角形" in content:
                if "滑动" in content and ("a²" in content or "b²" in content):
                    errors.append("几何逻辑错误：四个直角三角形无法通过简单滑动重排成两个独立的正方形")
            
            # 检查面积计算逻辑
            if "面积" in content:
                if not re.search(r"(a\+b)\^?2", content):
                    errors.append("缺少大正方形面积计算：(a+b)²")
        
        return errors
    
    def _verify_calculations(self, content: str) -> List[str]:
        """验证数值计算"""
        errors = []
        
        # 检查3-4-5三角形的计算
        if "3" in content and "4" in content and "5" in content:
            # 验证 3² + 4² = 5²
            if not (9 + 16 == 25):  # 这个永远为真，但展示验证逻辑
                errors.append("数值计算错误：3² + 4² ≠ 5²")
        
        return errors

class CodeValidator:
    """代码验证器"""
    
    def validate_manim_code(self, code_path: str) -> Tuple[bool, float, List[str]]:
        """验证Manim代码的有效性"""
        errors = []
        score = 1.0
        
        try:
            # 语法检查
            with open(code_path, 'r', encoding='utf-8') as f:
                code_content = f.read()
            
            # Python语法验证
            try:
                ast.parse(code_content)
            except SyntaxError as e:
                errors.append(f"Python语法错误: {e}")
                score -= 0.5
            
            # Manim特定检查
            manim_errors = self._check_manim_specific_issues(code_content)
            if manim_errors:
                errors.extend(manim_errors)
                score -= 0.3
            
            # 几何变换可行性检查
            geometric_errors = self._check_geometric_feasibility(code_content)
            if geometric_errors:
                errors.extend(geometric_errors)
                score -= 0.4
            
            # 尝试实际渲染测试
            render_success = self._test_render(code_path)
            if not render_success:
                errors.append("代码无法成功渲染")
                score -= 0.3
            
        except Exception as e:
            errors.append(f"代码验证异常: {e}")
            score = 0
        
        is_valid = score >= 0.7
        return is_valid, max(0, score), errors
    
    def _check_manim_specific_issues(self, code: str) -> List[str]:
        """检查Manim特定问题"""
        errors = []
        
        # 检查必要的导入
        if "from manim import *" not in code and "import manim" not in code:
            errors.append("缺少Manim导入")
        
        # 检查Scene类继承
        if not re.search(r"class\s+\w+\([^)]*Scene[^)]*\):", code):
            errors.append("缺少正确的Scene类继承")
        
        # 检查construct方法
        if "def construct(self):" not in code:
            errors.append("缺少construct方法")
        
        return errors
    
    def _check_geometric_feasibility(self, code: str) -> List[str]:
        """检查几何变换的可行性"""
        errors = []
        
        # 检查不可能的几何变换
        if "ReplacementTransform" in code:
            # 检查是否尝试在不兼容的几何对象间变换
            if "square_c" in code and "square_ab" in code:
                errors.append("警告：尝试在几何上不兼容的对象间进行变换")
        
        return errors
    
    def _test_render(self, code_path: str) -> bool:
        """测试代码是否能成功渲染"""
        try:
            # 创建临时目录进行测试渲染
            with tempfile.TemporaryDirectory() as temp_dir:
                cmd = [
                    "manim", "-ql", "--disable_caching", 
                    f"--media_dir={temp_dir}", 
                    code_path
                ]
                
                result = subprocess.run(
                    cmd, 
                    capture_output=True, 
                    text=True, 
                    timeout=60  # 60秒超时
                )
                
                return result.returncode == 0
        except Exception:
            return False

class TeachingEffectivenessValidator:
    """教学效果验证器"""
    
    def __init__(self):
        self.teaching_principles = {
            "progressive_difficulty": "难度递进",
            "concrete_examples": "具体例子",
            "visual_aids": "视觉辅助",
            "clear_explanations": "清晰解释",
            "practical_applications": "实际应用"
        }
    
    def validate_teaching_quality(self, content: str, topic: str) -> Tuple[bool, float, List[str]]:
        """验证教学质量"""
        score = 0.0
        feedback = []
        
        # 检查教学原则的应用
        for principle, description in self.teaching_principles.items():
            principle_score = self._evaluate_principle(content, principle)
            score += principle_score * 0.2  # 每个原则占20%
            
            if principle_score < 0.5:
                feedback.append(f"缺少{description}的有效应用")
        
        # 检查内容结构
        structure_score = self._evaluate_content_structure(content)
        score = (score * 0.8) + (structure_score * 0.2)
        
        is_effective = score >= 0.7
        return is_effective, score, feedback
    
    def _evaluate_principle(self, content: str, principle: str) -> float:
        """评估特定教学原则的应用"""
        if principle == "progressive_difficulty":
            # 检查是否有步骤递进
            steps = re.findall(r"步骤\d+|阶段\d+", content)
            return min(1.0, len(steps) / 3)
        
        elif principle == "concrete_examples":
            # 检查具体数值例子
            numbers = re.findall(r"\d+", content)
            return min(1.0, len(set(numbers)) / 5)
        
        elif principle == "visual_aids":
            # 检查视觉描述
            visual_keywords = ["颜色", "动画", "图形", "标签", "高亮"]
            count = sum(1 for keyword in visual_keywords if keyword in content)
            return min(1.0, count / 3)
        
        elif principle == "clear_explanations":
            # 检查解释性文字
            explanation_keywords = ["因为", "所以", "因此", "由于", "说明"]
            count = sum(1 for keyword in explanation_keywords if keyword in content)
            return min(1.0, count / 3)
        
        elif principle == "practical_applications":
            # 检查实际应用
            application_keywords = ["应用", "实际", "生活", "例子", "场景"]
            count = sum(1 for keyword in application_keywords if keyword in content)
            return min(1.0, count / 2)
        
        return 0.5  # 默认分数
    
    def _evaluate_content_structure(self, content: str) -> float:
        """评估内容结构"""
        score = 0.0
        
        # 检查是否有明确的开始、中间、结束
        if any(word in content for word in ["介绍", "定义", "问题"]):
            score += 0.3
        
        if any(word in content for word in ["证明", "推导", "计算"]):
            score += 0.4
        
        if any(word in content for word in ["应用", "总结", "结论"]):
            score += 0.3
        
        return score

class AutoQualityAssuranceSystem:
    """自动质量保证系统主类"""
    
    def __init__(self):
        self.math_validator = MathematicalValidator()
        self.code_validator = CodeValidator()
        self.teaching_validator = TeachingEffectivenessValidator()
        self.min_quality_threshold = 0.75
        self.max_iterations = 3
    
    def evaluate_content_quality(self, content_path: str, topic: str) -> QualityScore:
        """评估内容质量"""
        with open(content_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 数学准确性验证
        math_valid, math_score, math_errors = self.math_validator.validate_mathematical_content(content, topic)
        
        # 教学效果验证
        teaching_valid, teaching_score, teaching_feedback = self.teaching_validator.validate_teaching_quality(content, topic)
        
        return QualityScore(
            mathematical_accuracy=math_score,
            logical_consistency=0.8,  # 简化实现
            code_validity=0.8,        # 在代码阶段单独验证
            teaching_effectiveness=teaching_score,
            visual_clarity=0.8        # 简化实现
        )
    
    def evaluate_code_quality(self, code_path: str) -> QualityScore:
        """评估代码质量"""
        code_valid, code_score, code_errors = self.code_validator.validate_manim_code(code_path)
        
        return QualityScore(
            mathematical_accuracy=0.8,  # 继承自内容阶段
            logical_consistency=0.8,
            code_validity=code_score,
            teaching_effectiveness=0.8,
            visual_clarity=0.8
        )
    
    def should_regenerate(self, quality_score: QualityScore) -> bool:
        """判断是否需要重新生成"""
        return quality_score.overall_score < self.min_quality_threshold
    
    def generate_improvement_feedback(self, quality_score: QualityScore, errors: List[str]) -> str:
        """生成改进建议"""
        feedback = "请根据以下问题改进内容：\n"
        
        if quality_score.mathematical_accuracy < 0.7:
            feedback += "- 修正数学逻辑错误\n"
        
        if quality_score.code_validity < 0.7:
            feedback += "- 修正代码实现问题\n"
        
        if quality_score.teaching_effectiveness < 0.7:
            feedback += "- 改进教学效果和清晰度\n"
        
        if errors:
            feedback += "\n具体错误：\n"
            for error in errors:
                feedback += f"- {error}\n"
        
        return feedback
