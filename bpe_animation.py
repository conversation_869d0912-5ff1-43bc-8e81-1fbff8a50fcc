from manim import *
import numpy as np
from prompts.professional_science_template import ProfessionalScienceTemplate


class BPEAnimationScene(ProfessionalScienceTemplate):
    """
    BPE算法动画演示场景
    展示"魔法积木造词记"的完整流程
    """
    
    def construct(self):
        # 必须调用
        self.setup_background()
        
        # 阶段1：开场引入与步骤1——初始化词表
        self.stage_1_initialization()
        
        # 阶段2：步骤2——第一次合并
        self.stage_2_first_merge()
        
        # 阶段3：步骤3——第二次合并
        self.stage_3_second_merge()
        
        # 阶段4：步骤4&5——达到预设次数与实际应用
        self.stage_4_application()
        
        # 阶段5：总结回顾
        self.stage_5_summary()
    
    def create_vocab_text(self, vocab_dict):
        """创建词汇表显示文本"""
        vocab_items = []
        for char, count in vocab_dict.items():
            if count > 0:
                vocab_items.append(f"{char}: {count}")
        vocab_str = " | ".join(vocab_items)
        return Text(vocab_str, font_size=20)
    
    def create_pairs_count_text(self, pairs_dict):
        """创建词对计数显示文本"""
        pairs_items = []
        for pair, count in pairs_dict.items():
            if count > 0:
                pairs_items.append(f"({pair[0]},{pair[1]}): {count}")
        pairs_str = " | ".join(pairs_items)
        return Text(pairs_str, font_size=16)
    
    def create_vocab_text_set(self, vocab_set):
        """创建最终词汇集合显示文本"""
        vocab_str = " | ".join(sorted(vocab_set))
        return Text(f"词表: {vocab_str}", font_size=20)
    
    def stage_1_initialization(self):
        """阶段1：开场引入与步骤1——初始化词表"""
        # 模板接口调用 - 初始状态
        self.title_group = self.create_title_region_content("魔法积木造词记")
        step_group = self.create_step_region_content("初始化词表")
        
        # 创建原始语料
        corpus_text_1 = Text("你好棒你真棒", font_size=32)
        corpus_text_2 = Text("你好酷你好棒", font_size=32)
        corpus_group = VGroup(corpus_text_1, corpus_text_2).arrange(DOWN, buff=0.3)
        
        # 创建切分后的汉字
        chars_1 = VGroup(*[Text(char, font_size=32) for char in "你好棒你真棒"]).arrange(RIGHT, buff=0.1)
        chars_2 = VGroup(*[Text(char, font_size=32) for char in "你好酷你好棒"]).arrange(RIGHT, buff=0.1)
        chars_group = VGroup(chars_1, chars_2).arrange(DOWN, buff=0.3)
        
        # 词表和词对计数
        vocab_dict = {'你': 4, '好': 4, '棒': 3, '真': 1, '酷': 1}
        vocab_text = self.create_vocab_text(vocab_dict)
        
        pairs_count_dict = {('你', '好'): 4, ('好', '棒'): 3, ('棒', '你'): 1, ('真', '棒'): 1, ('酷', '你'): 1}
        pairs_count_text = self.create_pairs_count_text(pairs_count_dict)
        
        # 组织主内容
        main_content = VGroup(corpus_group, vocab_text, pairs_count_text).arrange(DOWN, buff=0.5)
        main_group = self.create_main_region_content(main_content)
        
        # 辅助区域
        left_aux_items = VGroup(*[Text("• " + item, font_size=20) for item in ["原始语料", "当前文本序列"]])
        left_aux = self.create_left_auxiliary_content("数据", left_aux_items)
        
        right_aux_items = VGroup(*[Text("• " + item, font_size=20) for item in ["切分汉字"]])
        right_aux = self.create_right_auxiliary_content("核心操作", right_aux_items)
        
        result_group = self.create_result_region_content("初始词表共5个汉字")
        
        # 动画播放
        self.play(Write(self.title_group))
        self.wait(0.5)
        self.play(Write(step_group))
        self.wait(0.5)
        self.play(FadeIn(left_aux), FadeIn(right_aux))
        self.wait(0.5)
        self.play(Write(corpus_group))
        self.wait(1)
        
        # 汉字切分动画
        self.play(Transform(corpus_group, chars_group))
        self.wait(1)
        
        # 词表和计数出现
        vocab_pairs_group = VGroup(vocab_text, pairs_count_text).arrange(DOWN, buff=0.3)
        vocab_pairs_group.next_to(chars_group, DOWN, buff=0.5)
        self.play(FadeIn(vocab_pairs_group))
        self.play(Write(result_group))
        self.wait(1)
        
        # 保存当前状态用于下一阶段
        self.current_chars = chars_group
        self.current_vocab_pairs = vocab_pairs_group
        self.current_step = step_group
        self.current_left_aux = left_aux
        self.current_right_aux = right_aux
        self.current_result = result_group
    
    def stage_2_first_merge(self):
        """阶段2：步骤2——第一次合并"""
        # 步骤更新
        new_step = self.create_step_region_content("第一次合并")
        self.play(Transform(self.current_step, new_step))
        self.wait(0.5)
        
        # 更新右侧辅助区域
        right_aux_items = VGroup(*[Text("• " + item, font_size=18) for item in ["发现最高频对", "合并新'积木'", "更新序列与计数"]])
        new_right_aux = self.create_right_auxiliary_content("核心操作", right_aux_items)
        self.play(ReplacementTransform(self.current_right_aux, new_right_aux))
        
        # 高亮最高频对 '你', '好'
        chars_1 = self.current_chars[0]  # 第一行字符
        chars_2 = self.current_chars[1]  # 第二行字符
        
        # 高亮动画
        target_pairs = VGroup(
            VGroup(chars_1[0], chars_1[1]),  # 你好
            VGroup(chars_1[3], chars_1[4]),  # 你好
            VGroup(chars_2[0], chars_2[1]),  # 你好
            VGroup(chars_2[3], chars_2[4])   # 你好
        )
        
        self.play(*[pair.animate.set_color(YELLOW) for pair in target_pairs])
        self.wait(1)
        
        # 合并动画 - 创建新的合并词
        merged_chars_1 = VGroup(
            Text("你好", font_size=32),
            chars_1[2], chars_1[3], 
            Text("你好", font_size=32)
        ).arrange(RIGHT, buff=0.1)
        
        merged_chars_2 = VGroup(
            Text("你好", font_size=32),
            chars_2[2],
            Text("你好", font_size=32)
        ).arrange(RIGHT, buff=0.1)
        
        new_chars_group = VGroup(merged_chars_1, merged_chars_2).arrange(DOWN, buff=0.3)
        
        self.play(Transform(self.current_chars, new_chars_group))
        self.wait(1)
        
        # 更新词表和计数
        new_vocab_dict = {'你': 0, '好': 0, '棒': 3, '真': 1, '酷': 1, '你好': 4}
        new_vocab_text = self.create_vocab_text(new_vocab_dict)
        
        new_pairs_count_dict = {('你好', '棒'): 3, ('棒', '你'): 1, ('真', '棒'): 1, ('酷', '你'): 1}
        new_pairs_count_text = self.create_pairs_count_text(new_pairs_count_dict)
        
        new_vocab_pairs = VGroup(new_vocab_text, new_pairs_count_text).arrange(DOWN, buff=0.3)
        new_vocab_pairs.next_to(new_chars_group, DOWN, buff=0.5)
        
        self.play(ReplacementTransform(self.current_vocab_pairs, new_vocab_pairs))
        
        # 更新结果
        new_result = self.create_result_region_content("第一次合并，最高频'你好'")
        self.play(Transform(self.current_result, new_result))
        self.wait(1)
        
        # 更新状态
        self.current_chars = new_chars_group
        self.current_vocab_pairs = new_vocab_pairs
        self.current_right_aux = new_right_aux
    
    def stage_3_second_merge(self):
        """阶段3：步骤3——第二次合并"""
        # 步骤更新
        new_step = self.create_step_region_content("第二次合并")
        self.play(Transform(self.current_step, new_step))
        self.wait(0.5)
        
        # 高亮新的最高频对 '你好', '棒'
        chars_1 = self.current_chars[0]
        target_pair = VGroup(chars_1[0], chars_1[2])  # '你好', '棒'
        
        self.play(target_pair.animate.set_color(YELLOW))
        self.wait(1)
        
        # 第二次合并动画
        merged_chars_1_new = VGroup(
            Text("你好棒", font_size=32),
            chars_1[3]  # 保留 '你'
        ).arrange(RIGHT, buff=0.1)
        
        new_chars_group = VGroup(merged_chars_1_new, self.current_chars[1]).arrange(DOWN, buff=0.3)
        
        self.play(Transform(self.current_chars, new_chars_group))
        self.wait(1)
        
        # 更新词表和计数
        new_vocab_dict = {'你': 0, '好': 0, '棒': 0, '真': 1, '酷': 1, '你好': 4, '你好棒': 3}
        new_vocab_text = self.create_vocab_text(new_vocab_dict)
        
        new_pairs_count_dict = {('你好棒', '你'): 1, ('真', '棒'): 1, ('你好', '酷'): 1, ('酷', '你'): 1}
        new_pairs_count_text = self.create_pairs_count_text(new_pairs_count_dict)
        
        new_vocab_pairs = VGroup(new_vocab_text, new_pairs_count_text).arrange(DOWN, buff=0.3)
        new_vocab_pairs.next_to(new_chars_group, DOWN, buff=0.5)
        
        self.play(ReplacementTransform(self.current_vocab_pairs, new_vocab_pairs))
        
        # 更新结果
        new_result = self.create_result_region_content("第二次合并，最高频'你好棒'")
        self.play(Transform(self.current_result, new_result))
        self.wait(1)
        
        # 更新状态
        self.current_chars = new_chars_group
        self.current_vocab_pairs = new_vocab_pairs
    
    def stage_4_application(self):
        """阶段4：步骤4&5——达到预设次数与实际应用"""
        # 步骤4：达到预设次数
        new_step = self.create_step_region_content("达到预设次数")
        self.play(Transform(self.current_step, new_step))
        self.wait(0.5)
        
        # 更新右侧辅助区域
        right_aux_items = VGroup(*[Text("• " + item, font_size=20) for item in ["达到预设次数"]])
        new_right_aux = self.create_right_auxiliary_content("核心操作", right_aux_items)
        self.play(ReplacementTransform(self.current_right_aux, new_right_aux))
        
        # 显示最终词表
        final_vocab_set = {'你', '好', '棒', '真', '酷', '你好', '你好棒'}
        final_vocab_text = self.create_vocab_text_set(final_vocab_set)
        
        self.play(ReplacementTransform(self.current_vocab_pairs, final_vocab_text))
        
        # 更新结果
        new_result = self.create_result_region_content("最终词表共7个单元")
        self.play(Transform(self.current_result, new_result))
        self.wait(1)
        
        # 步骤5：实际应用
        app_step = self.create_step_region_content("实际应用：文本编码")
        self.play(Transform(self.current_step, app_step))
        self.wait(0.5)
        
        # 更新右侧辅助区域
        right_aux_items_app = VGroup(*[Text("• " + item, font_size=20) for item in ["从最长子词匹配"]])
        new_right_aux_app = self.create_right_auxiliary_content("核心操作", right_aux_items_app)
        self.play(ReplacementTransform(new_right_aux, new_right_aux_app))
        
        # 清理主内容区
        self.play(FadeOut(self.current_chars))
        self.wait(0.5)
        
        # 待编码句子
        sentence_to_encode = Text("你好棒真棒", font_size=36)
        self.play(FadeIn(sentence_to_encode))
        self.wait(1)
        
        # 编码过程动画
        # 1. '你好棒'
        rect1 = SurroundingRectangle(sentence_to_encode[0:3], color=RED, buff=0.1)
        self.play(Create(rect1))
        self.wait(0.5)
        
        encoded_result = Text("编码结果: ['你好棒',", font_size=24)
        encoded_result.next_to(sentence_to_encode, DOWN, buff=0.8)
        self.play(Write(encoded_result), FadeOut(rect1))
        self.wait(0.5)
        
        # 2. '真'
        rect2 = SurroundingRectangle(sentence_to_encode[3], color=GREEN, buff=0.1)
        self.play(Create(rect2))
        self.wait(0.5)
        
        encoded_result2 = Text("编码结果: ['你好棒', '真',", font_size=24)
        encoded_result2.move_to(encoded_result.get_center())
        self.play(Transform(encoded_result, encoded_result2), FadeOut(rect2))
        self.wait(0.5)
        
        # 3. '棒'
        rect3 = SurroundingRectangle(sentence_to_encode[4], color=BLUE, buff=0.1)
        self.play(Create(rect3))
        self.wait(0.5)
        
        encoded_result_final = Text("编码结果: ['你好棒', '真', '棒']", font_size=24)
        encoded_result_final.move_to(encoded_result.get_center())
        self.play(Transform(encoded_result, encoded_result_final), FadeOut(rect3))
        
        # 最终结果
        final_result = self.create_result_region_content("编码结果: ['你好棒', '真', '棒'] (分成3个子词)")
        self.play(Transform(self.current_result, final_result))
        self.wait(2)
        
        # 保存状态
        self.current_sentence = sentence_to_encode
        self.current_encoding = encoded_result
        self.current_vocab_display = final_vocab_text
        self.current_right_aux = new_right_aux_app
    
    def stage_5_summary(self):
        """阶段5：总结回顾"""
        # 清理主内容区
        self.play(
            FadeOut(self.current_sentence),
            FadeOut(self.current_encoding),
            FadeOut(self.current_vocab_display)
        )
        
        self.play(
            FadeOut(self.current_left_aux),
            FadeOut(self.current_right_aux)
        )
        
        # 总结标题
        final_title = self.create_title_region_content("BPE积木造词原理")
        self.play(Transform(self.title_group, final_title))
        
        final_step = self.create_step_region_content("感谢观看！")
        self.play(Transform(self.current_step, final_step))
        
        final_result_summary = self.create_result_region_content("理解BPE如何从字符构建有意义的子词")
        self.play(Transform(self.current_result, final_result_summary))
        self.wait(3)


if __name__ == "__main__":
    # 用于测试的主函数
    pass