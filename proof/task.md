# 勾股定理图形化证明动画生成任务

## 任务描述
生成manim代码演示勾股定理的图形化证明过程。

## 证明类型和结构

### 证明方法
**面积重排证明法**：通过移动重排相同图形元素，直观展示面积关系
- 核心思路：4个三角形 + c²正方形 = 4个三角形 + a²正方形 + b²正方形
- 通过元素重新排列证明 c² = a² + b²

### 动画结构要求
1. **第一阶段**：构建左侧原始配置
   - 外部框架正方形
   - 4个全等直角三角形首尾相接（黄色，边长a、b、c）
   - 1个c²正方形（橙色，倾斜放置在中心）

2. **第二阶段**：复制移动重排过程
   - 使用`copy().animate.shift()`将元素从左侧移动到右侧
   - 三角形重新排列组合
   - 保持原始元素在左侧不变

3. **第三阶段**：显示右侧最终配置并推导公式
   - 右侧形成独立的a²正方形和b²正方形
   - 标注面积关系：c² = a² + b²
   - 显示勾股定理公式

### 渲染要求
- **命令兼容**：确保可以用`manim -pql --progress_bar none`成功生成
- **平台兼容**：在macOS上完美运行

## 学习目标导向

### 教学效果
- **步骤清晰**：每个推理步骤都有明确的视觉展示
- **重点突出**：强调面积关系和最终的a² + b² = c²公式
- **逻辑连贯**：从直观的图形操作到抽象的数学公式
- **适用场景**：适合中学数学教学或科普展示

### 数学准确性
- **定理表述**：准确表述勾股定理的内容
- **证明逻辑**：几何证明的每一步都逻辑严密
- **公式推导**：从面积计算到代数化简的完整过程

## 环境信息
- **操作系统**：macOS
- **已安装工具**：manim community edition
- **文档查询**：使用`context7`的`get-library-docs`工具查询`/manimcommunity/manim`库文档
