"""
分镜间转场功能 - 基于序列化的场景状态生成转场视频
"""

import os
from typing import Optional

from loguru import logger


def generate_inter_scene_transition(
    from_scene_id: str,
    to_scene_id: str,
    transition_type: Optional[str] = None,
    transition_run_time: float = 1.0,
    quality: str = "l",
) -> Optional[str]:
    """
    便捷函数：直接渲染分镜间转场视频

    Args:
        from_scene_id: 源分镜ID
        to_scene_id: 目标分镜ID
        transition_type: 转场类型
        transition_run_time: 转场动画时长
        quality: 视频质量 ('l', 'm', 'h', 'k')

    Returns:
        是否成功渲染
    """

    quality_res_mapping = {
        "l": "480p15",
        "m": "720p30",
        "h": "1080p60",
        "p": "1440p60",
        "k": "2160p60",
    }

    class_name = f"Transition_{from_scene_id}_{to_scene_id}"
    output_video_file = f"transition_{from_scene_id}_{to_scene_id}_{transition_type or 'default'}"
    manim_code = f"""
import pickle
import os
import sys
from pathlib import Path
from typing import Optional

from loguru import logger
from manim import *

# 获取项目根目录
current_file = Path(__file__).resolve()
project_root = current_file.parent.parent  # 从 output/ 目录向上两级到项目根目录
sys.path.insert(0, str(project_root))

from dsl.v2.core.scene import FeynmanScene
from dsl.v2.core.transition_effects import TransitionManager

config.output_file = "{output_video_file}"

class {class_name}(FeynmanScene):
    def construct(self):
        self.add_background()
        try:
            # 加载两个分镜的状态
            from_mobj = self.load_scene_state("{from_scene_id}")
            to_mobj = self.load_scene_state("{to_scene_id}", is_old_mobject=False)

            if not from_mobj and not to_mobj:
                logger.warning(f"无法找到分镜状态: {from_scene_id} -> {to_scene_id}")
                return

            # 创建转场场景并渲染
            # 显示源对象
            if from_mobj:
                self.add(from_mobj)

            # 应用转场效果
            if from_mobj or to_mobj:
                TransitionManager.apply_transition(
                    scene=self,
                    old_mobj=from_mobj,
                    new_mobj=to_mobj,
                    transition_type="{transition_type}",
                    run_time={transition_run_time},
                )
        except Exception as e:
            logger.error(f"渲染转场视频失败: {{e}}")
            import traceback
            traceback.print_exc()
"""

    output_py_file = f"inter_scene_transition_{from_scene_id}_{to_scene_id}.py"
    output_base_name = output_py_file.replace(".py", "")
    output_file_path = os.path.join("output", output_py_file)
    with open(output_file_path, "w", encoding="utf-8") as f:
        f.write(manim_code)
    transition_video_file = f"media/videos/{output_base_name}/{quality_res_mapping[quality]}/{output_video_file}.mp4"
    # clear existing
    if os.path.exists(transition_video_file):
        os.remove(transition_video_file)

    cmd = f"manim -q{quality} {output_file_path} {class_name}"
    logger.info(f"Running: {cmd}")
    os.system(cmd)

    if os.path.exists(transition_video_file):
        reencoded_file = transition_video_file.replace(".mp4", "_reencoded.mp4")
        cmd = f"ffmpeg -v error -i {transition_video_file} -c:v libx264 -c:a aac -y {reencoded_file}"
        logger.info(f"Running: {cmd}")
        os.system(cmd)
        return reencoded_file
    else:
        logger.error(f"转场视频文件不存在: {transition_video_file}")
    return None


if __name__ == "__main__":
    res = generate_inter_scene_transition("Storyboard_7", "Storyboard_7", "transformation", 1.0, "h")
    print(res)
