#!/usr/bin/env python3
"""
自动重新生成和修正系统
实现智能重试、错误修正和质量优化
"""

import json
import time
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import subprocess
from dataclasses import dataclass

from auto_quality_assurance_system import AutoQualityAssuranceSystem, QualityScore

@dataclass
class GenerationAttempt:
    """生成尝试记录"""
    attempt_number: int
    quality_score: QualityScore
    errors: List[str]
    timestamp: float
    output_path: str

class KnowledgeBase:
    """知识库系统"""
    
    def __init__(self):
        self.verified_templates = {
            "勾股定理": {
                "proof_method": "四个直角三角形拼接法",
                "key_steps": [
                    "定义直角三角形和未知斜边",
                    "构造大正方形：四个三角形围成边长(a+b)的正方形",
                    "面积计算对比：(a+b)² = 4×(½ab) + c²",
                    "推导等式：a² + 2ab + b² = 2ab + c²",
                    "得出结论：a² + b² = c²",
                    "实际应用示例"
                ],
                "common_errors": [
                    "错误的三角形重排描述",
                    "缺少面积等式推导",
                    "几何变换不可行"
                ],
                "correct_approach": "使用面积计算对比，而非几何重排"
            }
        }
        
        self.manim_best_practices = {
            "geometric_proofs": {
                "avoid_patterns": [
                    "ReplacementTransform between incompatible geometries",
                    "Impossible geometric rearrangements"
                ],
                "recommended_patterns": [
                    "Use side-by-side comparison for area calculations",
                    "Highlight corresponding elements with colors",
                    "Use step-by-step formula derivation"
                ]
            }
        }
    
    def get_template_for_topic(self, topic: str) -> Optional[Dict]:
        """获取主题的标准模板"""
        return self.verified_templates.get(topic)
    
    def get_error_corrections(self, topic: str, errors: List[str]) -> List[str]:
        """根据错误获取修正建议"""
        corrections = []
        template = self.get_template_for_topic(topic)
        
        if template:
            for error in errors:
                if "几何逻辑错误" in error or "重排" in error:
                    corrections.append(template["correct_approach"])
                elif "面积" in error:
                    corrections.append("使用面积等式：(a+b)² = 4×(½ab) + c²")
                elif "公式" in error:
                    corrections.append("确保包含核心公式：a² + b² = c²")
        
        return corrections

class AutoRegenerationSystem:
    """自动重新生成系统"""
    
    def __init__(self):
        self.qa_system = AutoQualityAssuranceSystem()
        self.knowledge_base = KnowledgeBase()
        self.max_attempts = 5
        self.min_quality_threshold = 0.75
        self.attempt_history: List[GenerationAttempt] = []
    
    def generate_with_quality_assurance(
        self, 
        topic: str, 
        generation_command: str,
        output_path: str,
        content_type: str = "content"  # "content" or "code"
    ) -> Tuple[bool, QualityScore, str]:
        """
        带质量保证的生成过程
        
        Args:
            topic: 主题
            generation_command: 生成命令
            output_path: 输出路径
            content_type: 内容类型
            
        Returns:
            (成功标志, 质量分数, 最终输出路径)
        """
        
        for attempt in range(1, self.max_attempts + 1):
            print(f"🔄 第 {attempt}/{self.max_attempts} 次生成尝试...")
            
            # 执行生成命令
            success = self._execute_generation(generation_command, attempt)
            if not success:
                print(f"❌ 第 {attempt} 次生成命令执行失败")
                continue
            
            # 质量评估
            if content_type == "content":
                quality_score = self.qa_system.evaluate_content_quality(output_path, topic)
            else:
                quality_score = self.qa_system.evaluate_code_quality(output_path)
            
            print(f"📊 质量分数: {quality_score.overall_score:.2f}")
            
            # 记录尝试
            attempt_record = GenerationAttempt(
                attempt_number=attempt,
                quality_score=quality_score,
                errors=[],  # 简化实现
                timestamp=time.time(),
                output_path=output_path
            )
            self.attempt_history.append(attempt_record)
            
            # 检查是否达到质量要求
            if quality_score.overall_score >= self.min_quality_threshold:
                print(f"✅ 质量达标，生成成功！")
                return True, quality_score, output_path
            
            # 生成改进建议并重试
            if attempt < self.max_attempts:
                improvement_feedback = self._generate_improvement_feedback(
                    topic, quality_score, attempt
                )
                print(f"🔧 生成改进建议: {improvement_feedback}")
                
                # 更新生成命令以包含改进建议
                generation_command = self._enhance_command_with_feedback(
                    generation_command, improvement_feedback
                )
        
        # 所有尝试都失败，选择最佳结果
        best_attempt = max(self.attempt_history, key=lambda x: x.quality_score.overall_score)
        print(f"⚠️ 未达到最佳质量，返回最佳尝试 (分数: {best_attempt.quality_score.overall_score:.2f})")
        
        return False, best_attempt.quality_score, best_attempt.output_path
    
    def _execute_generation(self, command: str, attempt: int) -> bool:
        """执行生成命令"""
        try:
            # 为重试添加随机性参数
            if attempt > 1:
                command += f" --seed {attempt * 42}"
            
            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            return result.returncode == 0
        except Exception as e:
            print(f"命令执行异常: {e}")
            return False
    
    def _generate_improvement_feedback(
        self, 
        topic: str, 
        quality_score: QualityScore, 
        attempt: int
    ) -> str:
        """生成改进建议"""
        
        feedback_parts = []
        
        # 基于质量分数的通用建议
        if quality_score.mathematical_accuracy < 0.7:
            template = self.knowledge_base.get_template_for_topic(topic)
            if template:
                feedback_parts.append(f"使用正确的证明方法: {template['correct_approach']}")
                feedback_parts.extend(template['key_steps'])
        
        if quality_score.teaching_effectiveness < 0.7:
            feedback_parts.extend([
                "增加更多具体的数值例子",
                "改进步骤之间的逻辑连接",
                "添加更清晰的视觉描述"
            ])
        
        if quality_score.code_validity < 0.7:
            feedback_parts.extend([
                "避免不可能的几何变换",
                "使用并列对比而非复杂的对象变换",
                "确保所有Manim代码语法正确"
            ])
        
        # 基于尝试次数的递进建议
        if attempt == 2:
            feedback_parts.append("重点关注数学逻辑的正确性")
        elif attempt == 3:
            feedback_parts.append("简化复杂的几何变换，使用更直观的方法")
        elif attempt >= 4:
            feedback_parts.append("采用最保守和经过验证的方法")
        
        return " | ".join(feedback_parts)
    
    def _enhance_command_with_feedback(self, original_command: str, feedback: str) -> str:
        """根据反馈增强生成命令"""
        
        # 简化实现：将反馈作为环境变量传递
        enhanced_command = f'IMPROVEMENT_FEEDBACK="{feedback}" {original_command}'
        
        return enhanced_command
    
    def get_generation_statistics(self) -> Dict:
        """获取生成统计信息"""
        if not self.attempt_history:
            return {}
        
        scores = [attempt.quality_score.overall_score for attempt in self.attempt_history]
        
        return {
            "total_attempts": len(self.attempt_history),
            "best_score": max(scores),
            "average_score": sum(scores) / len(scores),
            "final_score": scores[-1],
            "improvement": scores[-1] - scores[0] if len(scores) > 1 else 0
        }

class MultiCandidateGenerator:
    """多候选生成器"""
    
    def __init__(self):
        self.regeneration_system = AutoRegenerationSystem()
    
    def generate_multiple_candidates(
        self, 
        topic: str, 
        base_command: str, 
        output_dir: str,
        num_candidates: int = 3
    ) -> Tuple[str, QualityScore]:
        """
        生成多个候选并选择最佳
        
        Returns:
            (最佳候选路径, 质量分数)
        """
        
        candidates = []
        
        for i in range(num_candidates):
            print(f"🎯 生成候选 {i+1}/{num_candidates}...")
            
            candidate_output = f"{output_dir}/candidate_{i+1}"
            candidate_command = f"{base_command} --output {candidate_output}"
            
            success, quality_score, output_path = self.regeneration_system.generate_with_quality_assurance(
                topic=topic,
                generation_command=candidate_command,
                output_path=candidate_output,
                content_type="content"
            )
            
            if success:
                candidates.append((output_path, quality_score))
        
        if not candidates:
            raise Exception("所有候选生成都失败")
        
        # 选择最佳候选
        best_candidate = max(candidates, key=lambda x: x[1].overall_score)
        
        print(f"🏆 选择最佳候选，质量分数: {best_candidate[1].overall_score:.2f}")
        
        return best_candidate

# 使用示例
def main():
    """主函数示例"""
    
    regeneration_system = AutoRegenerationSystem()
    
    # 示例：生成勾股定理内容
    success, quality_score, output_path = regeneration_system.generate_with_quality_assurance(
        topic="勾股定理",
        generation_command="python agents/example_explain_agent_refactor.py",
        output_path="output/勾股定理/example_explain.md",
        content_type="content"
    )
    
    if success:
        print(f"✅ 生成成功！质量分数: {quality_score.overall_score:.2f}")
        print(f"📁 输出路径: {output_path}")
    else:
        print(f"⚠️ 生成未达到最佳质量，但已完成")
    
    # 打印统计信息
    stats = regeneration_system.get_generation_statistics()
    print(f"📊 生成统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")

if __name__ == "__main__":
    main()
