# Prompt对战工具使用指南

## 🎯 工具简介

Prompt对战工具是一个用于比较两种不同prompt生成的例子质量的评估系统。它可以帮助您：

- 对比现有Agent和新Prompt的生成效果
- 通过大模型进行客观的质量评估
- 记录对战历史，方便复盘和改进
- 从多个维度分析教学内容的优缺点

## 🚀 快速开始

### 基本使用

```bash
# 运行对战工具
python run_prompt_battle.py "主题" "目的"

# 示例
python run_prompt_battle.py "递归算法" "为计算机科学学生解释递归概念，通过具体例子展示递归的思维方式和实现原理"
```

### 命令行参数

```bash
# 使用自定义prompt文件
python tools/prompt_battle_tool.py "主题" "目的" --prompt-file "custom_prompt.py"

# 查看对战历史
python tools/prompt_battle_tool.py --history
```

## 📊 评估维度

工具从以下4个维度对生成的例子进行评估：

### 1. 教学效果（40分）
- **概念理解深度**：是否能帮助理解概念本质
- **逻辑清晰度**：步骤是否清晰、连贯
- **认知负荷**：是否适合目标受众的认知水平

### 2. 内容质量（30分）
- **准确性**：概念解释是否准确
- **完整性**：是否涵盖了关键要点
- **实用性**：是否包含实际应用价值

### 3. 表达效果（20分）
- **生动性**：例子是否生动有趣
- **类比质量**：类比是否恰当、易懂
- **语言表达**：是否简洁明了

### 4. 动画适配性（10分）
- **可视化程度**：是否便于制作动画
- **步骤可操作性**：每个步骤是否具体可演示

## 📁 输出文件结构

```
output/prompt_battles/
├── battle_YYYYMMDD_HHMMSS.json    # 详细对战结果
└── battle_summary.jsonl           # 对战历史摘要
```

### 详细结果文件格式

```json
{
  "metadata": {
    "timestamp": "20250723_224329",
    "topic": "递归算法",
    "purpose": "为计算机科学学生解释递归概念...",
    "new_prompt_file": "prompt.py"
  },
  "examples": {
    "agent_example": "现有Agent生成的例子内容",
    "prompt_example": "新Prompt生成的例子内容"
  },
  "analysis": {
    "overall_winner": "A" | "B" | "平局",
    "overall_score": {
      "example_A": 85,
      "example_B": 90
    },
    "detailed_analysis": {
      "teaching_effectiveness": {...},
      "content_quality": {...},
      "expression_effectiveness": {...},
      "animation_adaptability": {...}
    },
    "strengths_and_weaknesses": {...},
    "improvement_suggestions": {...},
    "conclusion": "综合结论和推荐理由"
  }
}
```

## 🔧 工具架构

### 核心组件

1. **PromptBattleTool**: 主要的对战工具类
2. **现有Agent调用**: 使用`agents/example_explain_agent_refactor.py`
3. **新Prompt调用**: 使用`prompt.py`中的优化prompt
4. **大模型评估**: 使用配置的模型进行客观分析

### 工作流程

```mermaid
graph TD
    A[输入主题和目的] --> B[现有Agent生成例子]
    A --> C[新Prompt生成例子]
    B --> D[大模型分析对比]
    C --> D
    D --> E[生成评估报告]
    E --> F[保存结果文件]
    F --> G[显示对战摘要]
```

## 📈 使用示例

### 示例1：基础对战

```bash
python run_prompt_battle.py "贪心算法" "为初学者解释复杂概念算法，用通俗易懂的语言，绝妙的视觉动画演示，结合生活化的例子，帮助理解核心概念和原理"
```

**输出示例：**
```
🥊 PROMPT 对战结果
================================================================================
📋 主题: 递归算法
🎯 目的: 为计算机科学学生解释递归概念，通过具体例子展示递归的思维方式和实现原理
⏰ 时间: 20250723_224329

🏆 获胜者: B
📊 得分: Agent例子 78 vs Prompt例子 90

📈 详细分析:
  • 教学效果: B (38 vs 32)
  • 内容质量: B (29 vs 25)
  • 表达效果: B (19 vs 15)
  • 动画适配性: B (9 vs 6)

💡 结论: 新Prompt在教学设计方面表现出色...
```

### 示例2：查看历史记录

```bash
python tools/prompt_battle_tool.py --history
```

## 🛠️ 自定义配置

### 修改评估模型

在`config/config.yaml`中修改模型配置：

```yaml
model:
  platform: "openrouter"
  type: "google/gemini-2.5-flash"
  # 其他配置...
```

### 自定义Prompt文件

创建自己的prompt文件，然后使用：

```bash
python tools/prompt_battle_tool.py "主题" "目的" --prompt-file "my_custom_prompt.py"
```

## 🔍 故障排除

### 常见问题

1. **Agent生成失败**
   - 检查配置文件是否正确
   - 确认API密钥有效
   - 查看日志文件`logs/prompt_battle.log`

2. **Prompt解析错误**
   - 确保prompt文件格式正确
   - 检查变量占位符`{主题}`和`{目的}`

3. **分析结果异常**
   - 检查模型配置
   - 确认网络连接正常

### 日志文件

- 主日志：`logs/prompt_battle.log`
- Agent日志：查看Agent相关日志文件

## 📝 最佳实践

1. **选择合适的主题**：选择具体、明确的概念进行对战
2. **明确目的描述**：详细描述目标受众和教学目标
3. **定期复盘**：查看历史记录，分析改进趋势
4. **迭代优化**：根据评估结果持续改进prompt设计

## 🤝 贡献指南

如果您想改进这个工具：

1. 在`tools/prompt_battle_tool.py`中添加新的评估维度
2. 在分析prompt中调整评估标准
3. 扩展输出格式和可视化功能
4. 添加更多的对比分析功能

---

**注意**：这个工具依赖于大模型的客观评估能力，结果仅供参考。建议结合人工评估来做最终决策。
