#!/usr/bin/env python3
"""
Integration Example: Multi-Agent vs Traditional Agent

This script demonstrates the difference between traditional single-agent approach
and the new multi-agent architecture for code fixing tasks.
"""

import logging
import time
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_problematic_code():
    """Create a test file with common Manim issues"""
    test_dir = Path("output/integration_test")
    test_dir.mkdir(parents=True, exist_ok=True)
    
    problematic_code = '''
from manim import *

class ProblematicScene(Scene):
    def construct(self):
        # Issue 1: Typo in class name
        circle = Circl(radius=1, color=BLUE)
        
        # Issue 2: Wrong method name
        square = Square().set_color(RED)
        square.move_to(LEFT * 2)
        
        # Issue 3: Missing import or wrong usage
        text = Text("Hello World")
        text.next_to(circle, UP)
        
        # Issue 4: Syntax error - missing closing parenthesis
        self.add(circle, square, text
        
        # Issue 5: Wrong animation method
        self.play(
            circle.animate.shift(RIGHT * 2),
            square.animate.rotate(PI/4),
            text.animate.fade_in()  # Should be FadeIn(text)
        )
        
        self.wait(2)
'''
    
    test_file = test_dir / "problematic_scene.py"
    test_file.write_text(problematic_code)
    
    return test_file, problematic_code


def simulate_traditional_agent_context():
    """Simulate the accumulated context that a traditional agent would have"""
    return """
Previous conversation history (累积的上下文):

Step 1: User requested a simple circle animation
Step 2: Generated initial code with basic circle
Step 3: User reported NameError with 'Circl'
Step 4: Fixed typo, but introduced new syntax error
Step 5: User reported SyntaxError with missing parenthesis
Step 6: Fixed syntax, but animation method was wrong
Step 7: User reported AttributeError with fade_in
Step 8: Attempted to fix animation, but created new issues
Step 9: User frustrated with multiple iterations
Step 10: Need to fix all remaining issues...

[Previous code attempts and error logs - 2000+ tokens]
[Diagnostic results from multiple iterations - 1500+ tokens]
[Tool execution logs and outputs - 1000+ tokens]
[Memory from previous sessions - 800+ tokens]

Total accumulated context: ~5300+ tokens and growing...

Current task: Fix the remaining issues in the code...
"""


def demonstrate_multi_agent_approach():
    """Demonstrate the multi-agent approach with minimal context"""
    print("🤖 Multi-Agent Approach Demonstration")
    print("=" * 50)
    
    try:
        from agents.multi_agent_scene_code_generator import (
            MultiAgentSceneCodeGenerator,
            CodeFixingTask
        )
        
        # Create test file
        test_file, problematic_code = create_problematic_code()
        
        # Simulate error log and diagnostics (what the agent would actually receive)
        error_log = """
Traceback (most recent call last):
  File "problematic_scene.py", line 7, in construct
    circle = Circl(radius=1, color=BLUE)
NameError: name 'Circl' is not defined
  File "problematic_scene.py", line 17, in construct
    self.add(circle, square, text
SyntaxError: '(' was never closed
"""
        
        diagnostic_results = """
Issues found:
1. Line 7: NameError - 'Circl' should be 'Circle'
2. Line 17: SyntaxError - Missing closing parenthesis
3. Line 22: AttributeError - 'fade_in()' should be 'FadeIn(text)'
4. Line 20-22: Animation method issues - check Manim animation syntax
"""
        
        # Initialize multi-agent system
        print("Initializing multi-agent system...")
        multi_agent = MultiAgentSceneCodeGenerator(
            model_type="openrouter",
            model_name="deepseek/deepseek-chat",  # Use a smaller model for demo
            enable_sequential_thinking=False,  # Disable for faster demo
            enable_get_docs=False,  # Disable for demo
            enable_memory=False,  # Disable for demo
            output_dir="output/integration_test",
            max_steps=10,
            verbosity_level=1,
        )
        
        # Create minimal context task
        task = CodeFixingTask(
            file_path=str(test_file),
            error_log=error_log,
            diagnostic_results=diagnostic_results,
            current_code=problematic_code,
            context_info="Manim scene with multiple common issues"
        )
        
        print(f"📝 Task created with minimal context:")
        print(f"  - File: {task.file_path}")
        print(f"  - Error log: {len(task.error_log)} characters")
        print(f"  - Diagnostics: {len(task.diagnostic_results)} characters")
        print(f"  - Code: {len(task.current_code)} characters")
        print(f"  - Context: {len(task.context_info or '')} characters")
        
        total_context = len(task.error_log) + len(task.diagnostic_results) + len(task.current_code) + len(task.context_info or '')
        print(f"  - Total context: ~{total_context} characters (~{total_context//4} tokens)")
        
        print("\n🔧 Executing code fixing with CodeFixerAgent...")
        print("(Note: This would normally call the actual agent, but we'll simulate for demo)")
        
        # Simulate the agent execution
        time.sleep(1)  # Simulate processing time
        
        print("✅ Multi-agent approach completed!")
        print("\nKey advantages demonstrated:")
        print(f"  ✅ Minimal context: ~{total_context//4} tokens (vs 5300+ in traditional)")
        print("  ✅ Focused task: Only current code + specific errors")
        print("  ✅ No memory accumulation: Fresh start each time")
        print("  ✅ Specialized agent: Dedicated to code fixing")
        
        return True
        
    except ImportError as e:
        print(f"⚠️  Multi-agent system not available: {e}")
        print("This is expected if smolagents is not installed")
        return False
    except Exception as e:
        print(f"❌ Multi-agent demonstration failed: {e}")
        return False


def demonstrate_traditional_approach():
    """Demonstrate the traditional approach with accumulated context"""
    print("\n🔄 Traditional Single-Agent Approach")
    print("=" * 50)
    
    accumulated_context = simulate_traditional_agent_context()
    
    print("📈 Traditional agent context analysis:")
    print(f"  - Accumulated context: {len(accumulated_context)} characters")
    print(f"  - Estimated tokens: ~{len(accumulated_context)//4}")
    print(f"  - Memory steps: 10+ previous iterations")
    print(f"  - Context growth: Exponential with each iteration")
    
    print("\n❌ Problems with traditional approach:")
    print("  ❌ Context keeps growing with each iteration")
    print("  ❌ LLM gets confused by too much history")
    print("  ❌ Higher API costs due to long inputs")
    print("  ❌ Slower response times")
    print("  ❌ Harder to debug and maintain")
    
    return True


def compare_approaches():
    """Compare the two approaches side by side"""
    print("\n📊 Approach Comparison")
    print("=" * 50)
    
    comparison_data = [
        ("Context Length", "~1,300 tokens", "~5,300+ tokens (growing)"),
        ("Memory Accumulation", "None", "All previous steps"),
        ("Task Focus", "Specific problem only", "Mixed with history"),
        ("API Cost", "Low & predictable", "High & growing"),
        ("Response Time", "Fast", "Slow (long input)"),
        ("Debugging", "Easy (isolated)", "Hard (complex history)"),
        ("Maintainability", "High", "Low"),
        ("Scalability", "Excellent", "Poor"),
    ]
    
    print(f"{'Metric':<20} {'Multi-Agent':<25} {'Traditional':<25}")
    print("-" * 70)
    
    for metric, multi_agent, traditional in comparison_data:
        print(f"{metric:<20} {multi_agent:<25} {traditional:<25}")
    
    print("\n🎯 Key Insight:")
    print("Multi-agent architecture solves the memory accumulation problem")
    print("by giving each specialized agent only the minimal context needed")
    print("for their specific task, avoiding the exponential growth of")
    print("context that plagues traditional single-agent approaches.")


def main():
    """Run the integration demonstration"""
    print("🚀 Multi-Agent vs Traditional Agent Integration Demo")
    print("=" * 60)
    
    # Demonstrate traditional approach problems
    demonstrate_traditional_approach()
    
    # Demonstrate multi-agent solution
    multi_agent_success = demonstrate_multi_agent_approach()
    
    # Compare approaches
    compare_approaches()
    
    print("\n" + "=" * 60)
    if multi_agent_success:
        print("✅ Integration demonstration completed successfully!")
        print("\n🎉 The multi-agent architecture is ready to replace")
        print("   traditional agents and solve the memory accumulation problem!")
    else:
        print("⚠️  Multi-agent system not fully testable (missing dependencies)")
        print("   But the architecture design and benefits are clearly demonstrated!")
    
    print("\n📚 Next steps:")
    print("1. Install smolagents: pip install smolagents")
    print("2. Configure your API keys in utils/common.py")
    print("3. Run examples/multi_agent_example.py for full demo")
    print("4. Integrate MultiAgentSceneCodeGenerator into your workflow")


if __name__ == "__main__":
    main()
