from manim import *
import sys
import os

# 添加prompts目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'prompts'))

from professional_science_template import ProfessionalScienceTemplate


class BPEAlgorithmScene(ProfessionalScienceTemplate):
    """
    BPE算法教学动画场景 - 魔法积木造词记

    场景主题：BPE算法实战
    布局策略：完整布局
    总时长估计：90秒
    动画复杂度：复杂
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 存储当前状态的变量
        self.current_chars_1 = None
        self.current_chars_2 = None
        self.current_vocab_text = None
        self.current_pairs_count_text = None
        self.current_step_group = None
        self.current_result_group = None
        self.current_left_aux = None
        self.current_right_aux = None

    def create_vocab_text(self, vocab_dict):
        """创建词表显示文本"""
        vocab_items = []
        for word, count in vocab_dict.items():
            if count > 0:  # 只显示计数大于0的词
                vocab_items.append(f"'{word}': {count}")

        vocab_str = "词表: {" + ", ".join(vocab_items) + "}"
        return Text(vocab_str, font_size=20, color=WHITE)

    def create_pairs_count_text(self, pairs_dict):
        """创建词对计数显示文本"""
        pairs_items = []
        for pair, count in pairs_dict.items():
            if count > 0:  # 只显示计数大于0的词对
                pairs_items.append(f"('{pair[0]}','{pair[1]}'): {count}")

        pairs_str = "词对计数: {" + ", ".join(pairs_items) + "}"
        return Text(pairs_str, font_size=16, color=GRAY)

    def create_vocab_text_set(self, vocab_set):
        """创建词表集合显示文本"""
        vocab_list = list(vocab_set)
        vocab_str = "最终词表: {" + ", ".join([f"'{word}'" for word in vocab_list]) + "}"
        return Text(vocab_str, font_size=20, color=WHITE)

    def construct(self):
        """场景构建主函数"""
        # 设置背景
        self.setup_background()

        # 执行5个阶段的动画
        self.stage_1_initialization()
        self.stage_2_first_merge()
        self.stage_3_second_merge()
        self.stage_4_completion_and_application()
        self.stage_5_summary()

    def stage_1_initialization(self):
        """阶段1：开场引入与步骤1——初始化词表（20秒）"""

        # 创建标题和步骤
        title_group = self.create_title_region_content("魔法积木造词记")
        step_group = self.create_step_region_content("初始化词表")
        self.current_step_group = step_group

        # 创建原始语料
        corpus_text_1 = Text("你好棒你真棒", font_size=32, color=WHITE)
        corpus_text_2 = Text("你好酷你好棒", font_size=32, color=WHITE)
        corpus_text_1.move_to(UP * 1.2)
        corpus_text_2.move_to(UP * 0.4)
        corpus_display = VGroup(corpus_text_1, corpus_text_2)

        # 创建切分后的汉字
        chars_1 = VGroup(*[Text(char, font_size=32, color=WHITE) for char in "你好棒你真棒"])
        chars_2 = VGroup(*[Text(char, font_size=32, color=WHITE) for char in "你好酷你好棒"])
        chars_1.arrange(RIGHT, buff=0.2).move_to(UP * 1.2)
        chars_2.arrange(RIGHT, buff=0.2).move_to(UP * 0.4)

        self.current_chars_1 = chars_1
        self.current_chars_2 = chars_2

        # 创建词表和词对计数
        vocab_dict = {'你': 4, '好': 4, '棒': 3, '真': 1, '酷': 1}
        vocab_text = self.create_vocab_text(vocab_dict)
        vocab_text.move_to(DOWN * 0.4)
        self.current_vocab_text = vocab_text

        pairs_count_dict = {('你', '好'): 4, ('好', '棒'): 3, ('棒', '你'): 1, ('真', '棒'): 1, ('酷', '你'): 1}
        pairs_count_text = self.create_pairs_count_text(pairs_count_dict)
        pairs_count_text.move_to(DOWN * 1.2)
        self.current_pairs_count_text = pairs_count_text

        # 创建主内容组
        main_content = VGroup(corpus_display, vocab_text, pairs_count_text)
        main_group = self.create_main_region_content(main_content)

        # 创建辅助区域
        left_aux = self.create_left_auxiliary_content("数据", ["原始语料", "当前文本序列"])
        right_aux = self.create_right_auxiliary_content("核心操作", ["切分汉字"])
        self.current_left_aux = left_aux
        self.current_right_aux = right_aux

        # 创建结果区域
        result_group = self.create_result_region_content("初始词表共5个汉字")
        self.current_result_group = result_group

        # 动画序列
        self.play(Write(title_group))
        self.play(Write(step_group))
        self.play(FadeIn(left_aux), FadeIn(right_aux))
        self.play(Write(corpus_display))

        # 汉字切分动画
        self.play(
            Transform(corpus_text_1, chars_1),
            Transform(corpus_text_2, chars_2)
        )
        self.wait(1)

        # 显示词表和计数
        self.play(FadeIn(vocab_text), FadeIn(pairs_count_text))
        self.play(Write(result_group))
        self.wait(2)

    def stage_2_first_merge(self):
        """阶段2：步骤2——第一次合并（25秒）"""

        # 更新步骤
        new_step = self.create_step_region_content("第一次合并")
        self.play(Transform(self.current_step_group, new_step))
        self.wait(0.5)

        # 更新右侧辅助区域
        new_right_aux = self.create_right_auxiliary_content("核心操作", ["发现最高频对", "合并新'积木'", "更新序列与计数"])
        self.play(ReplacementTransform(self.current_right_aux, new_right_aux))
        self.current_right_aux = new_right_aux

        # 高亮最高频对 '你', '好'
        target_pairs = []
        # 在第一个句子中找到 '你好' 对
        for i in range(len(self.current_chars_1) - 1):
            if (self.current_chars_1[i].text == '你' and
                i + 1 < len(self.current_chars_1) and
                self.current_chars_1[i + 1].text == '好'):
                target_pairs.append(VGroup(self.current_chars_1[i], self.current_chars_1[i + 1]))

        # 在第二个句子中找到 '你好' 对
        for i in range(len(self.current_chars_2) - 1):
            if (self.current_chars_2[i].text == '你' and
                i + 1 < len(self.current_chars_2) and
                self.current_chars_2[i + 1].text == '好'):
                target_pairs.append(VGroup(self.current_chars_2[i], self.current_chars_2[i + 1]))

        # 高亮显示
        highlight_anims = []
        for pair in target_pairs:
            highlight_anims.extend([
                pair[0].animate.set_color(YELLOW),
                pair[1].animate.set_color(YELLOW)
            ])

        if highlight_anims:
            self.play(*highlight_anims)
            self.wait(1)

        # 合并动画 - 创建新的合并词
        merged_words = []
        for pair in target_pairs:
            merged_word = Text("你好", font_size=32, color=YELLOW)
            merged_word.move_to(pair.get_center())
            merged_words.append(merged_word)

        # 执行合并
        merge_anims = []
        for i, merged_word in enumerate(merged_words):
            merge_anims.append(Transform(target_pairs[i], merged_word))

        if merge_anims:
            self.play(*merge_anims)
            self.wait(1)

        # 更新词表和计数
        new_vocab_dict = {'你': 0, '好': 0, '棒': 3, '真': 1, '酷': 1, '你好': 4}
        new_vocab_text = self.create_vocab_text(new_vocab_dict)
        new_vocab_text.move_to(self.current_vocab_text.get_center())

        new_pairs_count_dict = {('你好', '棒'): 3, ('棒', '你'): 1, ('真', '棒'): 1, ('酷', '你'): 1}
        new_pairs_count_text = self.create_pairs_count_text(new_pairs_count_dict)
        new_pairs_count_text.move_to(self.current_pairs_count_text.get_center())

        self.play(
            ReplacementTransform(self.current_vocab_text, new_vocab_text),
            ReplacementTransform(self.current_pairs_count_text, new_pairs_count_text)
        )

        self.current_vocab_text = new_vocab_text
        self.current_pairs_count_text = new_pairs_count_text

        # 更新结果
        new_result = self.create_result_region_content("第一次合并，最高频'你好'")
        self.play(Transform(self.current_result_group, new_result))
        self.wait(2)

    def stage_3_second_merge(self):
        """阶段3：步骤3——第二次合并（25秒）"""

        # 更新步骤
        new_step = self.create_step_region_content("第二次合并")
        self.play(Transform(self.current_step_group, new_step))
        self.wait(0.5)

        # 识别最高频对 '你好', '棒'
        # 简化处理：直接创建高亮效果
        highlight_text = Text("识别最高频对: ('你好', '棒')", font_size=24, color=YELLOW)
        highlight_text.move_to(UP * 2)

        self.play(Write(highlight_text))
        self.wait(1)
        self.play(FadeOut(highlight_text))

        # 合并动画 - 创建 '你好棒'
        merged_text = Text("你好棒", font_size=32, color=YELLOW)
        merged_text.move_to(UP * 1.2)

        self.play(Write(merged_text))
        self.wait(1)

        # 更新词表和计数
        new_vocab_dict = {'你': 0, '好': 0, '棒': 0, '真': 1, '酷': 1, '你好': 1, '你好棒': 3}
        new_vocab_text = self.create_vocab_text(new_vocab_dict)
        new_vocab_text.move_to(self.current_vocab_text.get_center())

        new_pairs_count_dict = {('你好棒', '你'): 1, ('真', '棒'): 1, ('你好', '酷'): 1, ('酷', '你'): 1}
        new_pairs_count_text = self.create_pairs_count_text(new_pairs_count_dict)
        new_pairs_count_text.move_to(self.current_pairs_count_text.get_center())

        self.play(
            ReplacementTransform(self.current_vocab_text, new_vocab_text),
            ReplacementTransform(self.current_pairs_count_text, new_pairs_count_text)
        )

        self.current_vocab_text = new_vocab_text
        self.current_pairs_count_text = new_pairs_count_text

        # 更新结果
        new_result = self.create_result_region_content("第二次合并，最高频'你好棒'")
        self.play(Transform(self.current_result_group, new_result))
        self.wait(2)

    def stage_4_completion_and_application(self):
        """阶段4：步骤4——达到预设合并次数与步骤5——实际应用（20秒）"""

        # 步骤4：达到预设次数
        new_step = self.create_step_region_content("达到预设次数")
        self.play(Transform(self.current_step_group, new_step))
        self.wait(0.5)

        # 更新右侧辅助区域
        new_right_aux = self.create_right_auxiliary_content("核心操作", ["达到预设次数"])
        self.play(ReplacementTransform(self.current_right_aux, new_right_aux))
        self.current_right_aux = new_right_aux

        # 显示最终词表
        final_vocab_set = {'你', '好', '棒', '真', '酷', '你好', '你好棒'}
        final_vocab_text = self.create_vocab_text_set(final_vocab_set)
        final_vocab_text.move_to(self.current_vocab_text.get_center())

        self.play(ReplacementTransform(self.current_vocab_text, final_vocab_text))
        self.play(FadeOut(self.current_pairs_count_text))  # 词对计数不再需要

        self.current_vocab_text = final_vocab_text

        # 更新结果
        new_result = self.create_result_region_content("最终词表共7个单元")
        self.play(Transform(self.current_result_group, new_result))
        self.wait(1)

        # 步骤5：实际应用
        new_step = self.create_step_region_content("实际应用：文本编码")
        self.play(Transform(self.current_step_group, new_step))
        self.wait(0.5)

        # 更新右侧辅助区域
        new_right_aux = self.create_right_auxiliary_content("核心操作", ["从最长子词匹配"])
        self.play(ReplacementTransform(self.current_right_aux, new_right_aux))
        self.current_right_aux = new_right_aux

        # 清理主内容区域
        self.play(FadeOut(self.current_chars_1), FadeOut(self.current_chars_2))
        self.wait(0.5)

        # 待编码句子
        sentence_to_encode = Text("你好棒真棒", font_size=32, color=WHITE)
        sentence_to_encode.move_to(UP * 0.8)
        self.play(FadeIn(sentence_to_encode))
        self.wait(1)

        # 编码过程演示
        encoding_steps = [
            ("你好棒", RED),
            ("真", GREEN),
            ("棒", BLUE)
        ]

        encoded_parts = []

        for i, (part, color) in enumerate(encoding_steps):
            # 高亮匹配部分
            highlight_rect = SurroundingRectangle(
                sentence_to_encode[0:len(part)],
                color=color,
                buff=0.1
            )
            self.play(Create(highlight_rect))
            self.wait(0.5)

            # 显示编码结果
            encoded_part = Text(f"['{part}']", font_size=20, color=color)
            encoded_part.move_to(DOWN * (0.5 + i * 0.4))
            encoded_parts.append(encoded_part)

            self.play(Write(encoded_part), FadeOut(highlight_rect))
            self.wait(0.5)

        # 最终编码结果
        final_result = self.create_result_region_content("编码结果: ['你好棒', '真', '棒']")
        self.play(Transform(self.current_result_group, final_result))
        self.wait(2)

    def stage_5_summary(self):
        """阶段5：总结回顾（5秒）"""

        # 清理主内容区域
        self.play(
            *[FadeOut(mob) for mob in self.mobjects if mob not in [
                self.current_step_group,
                self.current_result_group
            ]]
        )

        # 最终标题和总结
        final_step = self.create_step_region_content("感谢观看！")
        final_result = self.create_result_region_content("理解BPE如何从字符构建有意义的子词")

        self.play(
            Transform(self.current_step_group, final_step),
            ReplacementTransform(self.current_result_group, final_result)
        )
        self.wait(2)
