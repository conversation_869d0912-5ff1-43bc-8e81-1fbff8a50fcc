#!/bin/bash

# 全自动质量保证教学视频生成流水线
# 集成多层验证、自动重试和质量优化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置参数
MAX_OVERALL_RETRIES=3
QUALITY_THRESHOLD=0.75
ENABLE_MULTI_CANDIDATE=true
NUM_CANDIDATES=3

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }
log_quality() { echo -e "${PURPLE}[QUALITY]${NC} $1"; }

# 获取主题配置
get_topic_from_config() {
    local config_file="config/config.yaml"
    
    if [ ! -f "$config_file" ]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    topic=$(python3 -c "
import yaml
try:
    with open('$config_file', 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    print(config['example_explain']['topic'])
except Exception as e:
    print('ERROR: ' + str(e))
    exit(1)
")
    
    if [[ $topic == ERROR:* ]]; then
        log_error "解析配置文件失败: $topic"
        exit 1
    fi
    
    echo "$topic"
}

# 质量评估函数
evaluate_quality() {
    local file_path="$1"
    local topic="$2"
    local content_type="$3"
    
    log_quality "评估质量: $file_path"
    
    # 调用Python质量评估系统
    quality_result=$(python3 -c "
import sys
sys.path.append('agents')
from auto_quality_assurance_system import AutoQualityAssuranceSystem

qa_system = AutoQualityAssuranceSystem()

if '$content_type' == 'content':
    quality_score = qa_system.evaluate_content_quality('$file_path', '$topic')
else:
    quality_score = qa_system.evaluate_code_quality('$file_path')

print(f'{quality_score.overall_score:.3f}')
print(f'Math: {quality_score.mathematical_accuracy:.3f}')
print(f'Logic: {quality_score.logical_consistency:.3f}')
print(f'Code: {quality_score.code_validity:.3f}')
print(f'Teaching: {quality_score.teaching_effectiveness:.3f}')
print(f'Visual: {quality_score.visual_clarity:.3f}')
")
    
    echo "$quality_result"
}

# 带质量保证的生成函数
generate_with_qa() {
    local command="$1"
    local description="$2"
    local output_file="$3"
    local topic="$4"
    local content_type="$5"
    local max_attempts="${6:-3}"
    
    log_step "$description"
    
    for attempt in $(seq 1 $max_attempts); do
        log_info "尝试 $attempt/$max_attempts: $description"
        
        # 执行生成命令
        if eval "$command"; then
            log_info "✅ 命令执行成功"
            
            # 检查输出文件是否存在
            if [ ! -f "$output_file" ]; then
                log_warning "输出文件不存在: $output_file"
                continue
            fi
            
            # 质量评估
            quality_result=$(evaluate_quality "$output_file" "$topic" "$content_type")
            quality_score=$(echo "$quality_result" | head -n1)
            
            log_quality "质量分数: $quality_score"
            echo "$quality_result" | tail -n+2 | while read line; do
                log_quality "  $line"
            done
            
            # 检查是否达到质量要求
            if (( $(echo "$quality_score >= $QUALITY_THRESHOLD" | bc -l) )); then
                log_info "✅ $description 质量达标 (分数: $quality_score)"
                return 0
            else
                log_warning "质量未达标 (分数: $quality_score, 要求: $QUALITY_THRESHOLD)"
                
                if [ $attempt -lt $max_attempts ]; then
                    log_info "准备重试..."
                    sleep 2
                fi
            fi
        else
            log_error "❌ 命令执行失败 (尝试 $attempt/$max_attempts)"
        fi
    done
    
    log_error "💥 $description 在 $max_attempts 次尝试后仍未达到质量要求"
    return 1
}

# 多候选生成函数
generate_multiple_candidates() {
    local base_command="$1"
    local description="$2"
    local output_dir="$3"
    local topic="$4"
    local content_type="$5"
    
    if [ "$ENABLE_MULTI_CANDIDATE" != "true" ]; then
        # 单候选模式
        local output_file="$output_dir"
        generate_with_qa "$base_command" "$description" "$output_file" "$topic" "$content_type"
        return $?
    fi
    
    log_step "$description (多候选模式)"
    
    local best_score=0
    local best_candidate=""
    local candidates_dir="${output_dir}_candidates"
    
    mkdir -p "$candidates_dir"
    
    # 生成多个候选
    for i in $(seq 1 $NUM_CANDIDATES); do
        log_info "生成候选 $i/$NUM_CANDIDATES"
        
        local candidate_file="${candidates_dir}/candidate_${i}"
        local candidate_command="$base_command"
        
        # 为不同候选添加变化参数
        if [ $i -gt 1 ]; then
            candidate_command="GENERATION_SEED=$((i * 42)) $candidate_command"
        fi
        
        if eval "$candidate_command > /dev/null 2>&1"; then
            # 复制输出到候选文件
            if [ -f "$output_dir" ]; then
                cp "$output_dir" "$candidate_file"
            fi
            
            # 评估候选质量
            if [ -f "$candidate_file" ]; then
                quality_result=$(evaluate_quality "$candidate_file" "$topic" "$content_type")
                candidate_score=$(echo "$quality_result" | head -n1)
                
                log_quality "候选 $i 质量分数: $candidate_score"
                
                # 更新最佳候选
                if (( $(echo "$candidate_score > $best_score" | bc -l) )); then
                    best_score=$candidate_score
                    best_candidate=$candidate_file
                fi
            fi
        fi
    done
    
    # 选择最佳候选
    if [ -n "$best_candidate" ] && [ -f "$best_candidate" ]; then
        cp "$best_candidate" "$output_dir"
        log_info "🏆 选择最佳候选，质量分数: $best_score"
        
        # 检查是否达到质量要求
        if (( $(echo "$best_score >= $QUALITY_THRESHOLD" | bc -l) )); then
            return 0
        else
            log_warning "最佳候选仍未达到质量要求"
            return 1
        fi
    else
        log_error "所有候选生成都失败"
        return 1
    fi
}

# 智能重试包装器
retry_with_intelligence() {
    local operation="$1"
    shift
    local args=("$@")
    
    for overall_attempt in $(seq 1 $MAX_OVERALL_RETRIES); do
        log_info "🔄 整体尝试 $overall_attempt/$MAX_OVERALL_RETRIES"
        
        if $operation "${args[@]}"; then
            return 0
        fi
        
        if [ $overall_attempt -lt $MAX_OVERALL_RETRIES ]; then
            log_warning "整体重试中..."
            sleep 5
        fi
    done
    
    return 1
}

# 主函数
main() {
    echo "=================================================="
    log_info "🎬 启动全自动质量保证教学视频生成流水线"
    echo "=================================================="
    
    # 检查依赖
    if ! command -v python3 &> /dev/null; then
        log_error "需要安装 Python3"
        exit 1
    fi
    
    if ! command -v bc &> /dev/null; then
        log_error "需要安装 bc (用于浮点数比较)"
        exit 1
    fi
    
    # 获取主题
    topic=$(get_topic_from_config)
    log_info "📋 主题: $topic"
    log_info "🎯 质量阈值: $QUALITY_THRESHOLD"
    log_info "🔄 最大重试次数: $MAX_OVERALL_RETRIES"
    log_info "👥 多候选模式: $ENABLE_MULTI_CANDIDATE"
    
    # 构建路径
    topic_dir="output/${topic}"
    example_explain_file="${topic_dir}/example_explain.md"
    vision_storyboard_file="${topic_dir}/vision_storyboard"
    code_output_path="${topic_dir}/code/${topic}"
    
    # 清理并创建目录
    if [ -d "$topic_dir" ]; then
        rm -rf "$topic_dir"
    fi
    mkdir -p "$topic_dir"
    
    # 步骤1: 生成例子解释
    echo ""
    log_step "=== 步骤 1: 生成例子解释 ==="
    if retry_with_intelligence generate_multiple_candidates \
        "python agents/example_explain_agent_refactor.py" \
        "生成例子解释" \
        "$example_explain_file" \
        "$topic" \
        "content"; then
        
        log_info "✅ 步骤1完成"
    else
        log_error "💥 步骤1失败，终止流水线"
        exit 1
    fi
    
    # 步骤2: 生成视觉故事板
    echo ""
    log_step "=== 步骤 2: 生成视觉故事板 ==="
    if retry_with_intelligence generate_multiple_candidates \
        "python agents/theorem_agents/vision_storyboard_agent.py '$example_explain_file' '$vision_storyboard_file'" \
        "生成视觉故事板" \
        "$vision_storyboard_file" \
        "$topic" \
        "content"; then
        
        log_info "✅ 步骤2完成"
    else
        log_error "💥 步骤2失败，终止流水线"
        exit 1
    fi
    
    # 步骤3: 生成场景代码
    echo ""
    log_step "=== 步骤 3: 生成场景代码 ==="
    if retry_with_intelligence generate_with_qa \
        "python scripts/run_claude_code_router.py '$vision_storyboard_file' '$code_output_path'" \
        "生成场景代码" \
        "${code_output_path}.py" \
        "$topic" \
        "code" \
        "2"; then  # 代码生成重试次数较少，因为成本较高
        
        log_info "✅ 步骤3完成"
    else
        log_error "💥 步骤3失败，终止流水线"
        exit 1
    fi
    
    # 最终质量检查
    echo ""
    log_step "=== 最终质量检查 ==="
    
    # 检查视频文件是否生成
    video_pattern="media/videos/${topic}/*/PythagoreanProof.mp4"
    video_files=($(find . -path "$video_pattern" 2>/dev/null))
    
    if [ ${#video_files[@]} -gt 0 ]; then
        video_file="${video_files[0]}"
        video_size=$(stat -f%z "$video_file" 2>/dev/null || stat -c%s "$video_file" 2>/dev/null)
        
        if [ "$video_size" -gt 100000 ]; then  # 至少100KB
            log_info "✅ 视频文件生成成功: $video_file (大小: ${video_size} 字节)"
        else
            log_warning "⚠️ 视频文件过小，可能有问题: $video_file"
        fi
    else
        log_warning "⚠️ 未找到视频文件"
    fi
    
    # 输出最终结果
    echo ""
    echo "=================================================="
    log_info "🎉 全自动流水线执行完成！"
    log_info "✨ 主题: $topic"
    log_info "📂 输出目录: $topic_dir"
    if [ ${#video_files[@]} -gt 0 ]; then
        log_info "🎥 视频文件: ${video_files[0]}"
    fi
    echo "=================================================="
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --threshold N    设置质量阈值 (0.0-1.0, 默认: 0.75)"
    echo "  -r, --retries N      设置最大重试次数 (默认: 3)"
    echo "  -c, --candidates N   设置候选数量 (默认: 3)"
    echo "  --no-multi          禁用多候选模式"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  QUALITY_THRESHOLD    质量阈值"
    echo "  MAX_OVERALL_RETRIES  最大重试次数"
    echo "  NUM_CANDIDATES       候选数量"
    echo "  ENABLE_MULTI_CANDIDATE  启用多候选模式"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--threshold)
            QUALITY_THRESHOLD="$2"
            shift 2
            ;;
        -r|--retries)
            MAX_OVERALL_RETRIES="$2"
            shift 2
            ;;
        -c|--candidates)
            NUM_CANDIDATES="$2"
            shift 2
            ;;
        --no-multi)
            ENABLE_MULTI_CANDIDATE=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 检查脚本是否被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
