#!/usr/bin/env python3
"""
测试CCR后台服务启动和停止
"""

import subprocess
import sys
import time
from pathlib import Path

# 添加scripts目录到路径
current_dir = Path(__file__).parent
scripts_dir = current_dir / "scripts"
sys.path.append(str(scripts_dir))

# 导入我们的脚本
from run_claude_code_router import start_ccr_service, stop_ccr_service

def check_ccr_status():
    """检查CCR服务状态"""
    try:
        # 尝试连接到CCR服务
        result = subprocess.run(
            ["curl", "-s", "http://127.0.0.1:3456/health"], 
            capture_output=True, 
            text=True, 
            timeout=5
        )
        if result.returncode == 0:
            return True, "服务运行中"
        else:
            return False, "服务未响应"
    except subprocess.TimeoutExpired:
        return False, "连接超时"
    except FileNotFoundError:
        # 如果没有curl，尝试其他方法
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', 3456))
            sock.close()
            if result == 0:
                return True, "端口开放"
            else:
                return False, "端口未开放"
        except Exception as e:
            return False, f"检查失败: {e}"
    except Exception as e:
        return False, f"检查异常: {e}"

def test_ccr_background_service():
    """测试CCR后台服务"""
    print("🧪 测试CCR后台服务启动和停止")
    print("=" * 50)
    
    # 1. 检查初始状态
    print("1️⃣ 检查初始服务状态...")
    is_running, status = check_ccr_status()
    print(f"初始状态: {status}")
    
    # 2. 启动服务
    print("\n2️⃣ 启动CCR服务...")
    start_success = start_ccr_service()
    
    if start_success:
        print("✅ 启动函数执行成功")
        
        # 等待服务完全启动
        print("⏳ 等待服务启动...")
        time.sleep(5)
        
        # 检查服务是否真的在运行
        is_running, status = check_ccr_status()
        print(f"启动后状态: {status}")
        
        if is_running:
            print("✅ CCR服务确实在后台运行")
        else:
            print("⚠️ CCR服务可能未正常启动")
    else:
        print("❌ 启动函数执行失败")
    
    # 3. 测试服务功能（简单测试）
    print("\n3️⃣ 测试服务功能...")
    try:
        import os
        env = os.environ.copy()
        env["ANTHROPIC_BASE_URL"] = "http://127.0.0.1:3456"
        env["ANTHROPIC_API_KEY"] = "sk-Lp2Gk32pn7RBia9i0U58tipQ5E4eEiXdPKAyp5lIYP2bAJVv"
        
        result = subprocess.run([
            "claude", "--print", "请回复OK"
        ], capture_output=True, text=True, env=env, timeout=30)
        
        if result.returncode == 0:
            print("✅ 服务功能正常")
            print(f"响应: {result.stdout.strip()[:50]}...")
        else:
            print("⚠️ 服务功能测试失败")
            print(f"错误: {result.stderr[:100]}...")
            
    except subprocess.TimeoutExpired:
        print("⏰ 服务功能测试超时")
    except Exception as e:
        print(f"❌ 服务功能测试异常: {e}")
    
    # 4. 停止服务
    print("\n4️⃣ 停止CCR服务...")
    stop_ccr_service()
    
    # 等待服务完全停止
    print("⏳ 等待服务停止...")
    time.sleep(3)
    
    # 检查服务是否已停止
    is_running, status = check_ccr_status()
    print(f"停止后状态: {status}")
    
    if not is_running:
        print("✅ CCR服务已成功停止")
    else:
        print("⚠️ CCR服务可能仍在运行")
    
    print("\n" + "=" * 50)
    print("📊 后台服务测试完成")

if __name__ == "__main__":
    test_ccr_background_service()
