# Claude Code SDK 迁移说明

## 概述

`scripts/run_claude_code_router.py` 已经从使用命令行工具改造为使用 Anthropic Claude Code SDK。

## 主要变化

### 1. 依赖变化
- **保留**: `@musistudio/claude-code-router` (npm包，用于代理服务)
- **保留**: `@anthropic-ai/claude-code` (npm包)
- **新增**: `claude-code-sdk` (Python包)
- **新增**: `anyio` (Python包，用于异步处理)

### 2. 架构变化
- **保留**: CCR服务启动/停止逻辑（作为代理服务转发请求）
- **保留**: 配置文件复制逻辑
- **改为**: 使用Python SDK替代命令行调用
- **保留**: 环境变量设置（ANTHROPIC_BASE_URL, ANTHROPIC_API_KEY）

### 3. 新增功能
- **详细进度显示**: 根据不同消息类型显示详细的执行进度
  - 👤 用户消息发送状态
  - 🤖 Claude回复内容摘要
  - 🔧 工具使用情况（如文件操作、代码执行等）
  - 🚀 系统初始化信息（会话ID、可用工具、使用模型）
  - 📊 执行统计（轮数、耗时、成本）
  - ⚠️ 错误和警告信息

### 4. 功能保持不变
- 命令行接口完全相同
- 返回结果格式相同
- 文件读取和任务描述构建逻辑不变
- 视频文件查找逻辑不变

## 安装要求

### 1. 安装Node.js依赖
```bash
npm install -g @musistudio/claude-code-router @anthropic-ai/claude-code
```

### 2. 安装Python依赖
```bash
pip install claude-code-sdk anyio
```

### 3. 环境变量设置
脚本会自动设置以下环境变量：
- `ANTHROPIC_BASE_URL=http://127.0.0.1:3456`
- `ANTHROPIC_API_KEY=sk-Lp2Gk32pn7RBia9i0U58tipQ5E4eEiXdPKAyp5lIYP2bAJVv`

### 4. CCR服务说明
CCR (Claude Code Router) 服务作为代理服务运行，负责：
- 转发API请求到本地服务
- 管理认证和配置
- 脚本会自动启动和停止CCR服务

## 使用方法

使用方法完全不变：

```bash
python scripts/run_claude_code_router.py <scene_file> <output_file>
```

例如：
```bash
python scripts/run_claude_code_router.py test_scene.txt test_output
```

## 优势

1. **更稳定**: 使用SDK替代命令行调用，减少进程间通信问题
2. **更高效**: Python SDK调用比subprocess更高效
3. **保持兼容**: 保留CCR代理服务，确保现有配置继续有效
4. **更易维护**: 混合架构，结合SDK的稳定性和CCR的代理功能

## 注意事项

1. 确保已安装必要的Python依赖
2. 如果遇到导入错误，脚本会自动提示安装命令
3. 环境变量会在运行时自动设置，无需手动配置
4. 保持与原有接口的完全兼容性

## 测试

可以使用提供的测试场景文件进行测试：
```bash
python scripts/run_claude_code_router.py test_scene.txt test_hello_sdk
```

这将生成一个简单的"Hello Claude SDK!"动画。

## 进度显示示例

运行时会看到类似以下的详细进度信息：

```
🚀 正在运行Claude SDK...
🚀 系统初始化完成 - 会话ID: abc123-def456
🛠️  可用工具: 15 个
🧠 使用模型: anthropic/claude-sonnet-4
👤 用户消息已发送
🤖 Claude回复: 我将为您创建一个简单的Manim动画...
🔧 Claude正在使用工具: str_replace_editor, launch_process
🤖 Claude回复: 代码已生成并保存到文件...
✅ Claude SDK执行成功
📊 执行统计: 8轮对话, 耗时45230ms
💰 成本: $0.0234
⏱️  总耗时: 45.2秒 (API: 12.3秒)
```

这样的进度显示让用户能够清楚地了解：
- 当前执行到哪个阶段
- Claude正在使用哪些工具
- 执行的性能和成本信息
- 任何错误或警告信息
