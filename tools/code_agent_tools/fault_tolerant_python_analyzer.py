"""
Fault-Tolerant Python Code Analyzer

This module provides a comprehensive Python code analyzer that can continue analysis
even when encountering errors, similar to how C++ compilers work. It performs
multiple types of analysis to catch all possible issues in a single pass.
"""

import ast
import re
import sys
import traceback
from typing import List, Dict, Any, Tuple, Optional
import importlib.util
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, MagicMock
import types

from loguru import logger


class CodeIssue:
    """Represents a code issue found during analysis."""

    def __init__(self, severity: str, issue_type: str, line: int, column: int,
                 message: str, code_context: str = ""):
        self.severity = severity  # "error", "warning", "info"
        self.issue_type = issue_type  # "syntax", "import", "name", "type", "manim"
        self.line = line
        self.column = column
        self.message = message
        self.code_context = code_context

    def __str__(self):
        severity_icon = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}
        icon = severity_icon.get(self.severity, "•")
        return f"{icon} {self.severity.upper()} [{self.issue_type}] Line {self.line}: {self.message}"


class FaultTolerantPythonAnalyzer:
    """
    A fault-tolerant Python code analyzer that continues analysis even when errors occur.

    This analyzer performs multiple passes over the code:
    1. Syntax analysis using AST
    2. Import validation
    3. Name resolution analysis
    4. Manim-specific validation
    5. Runtime simulation (limited)
    """

    def __init__(self):
        self.issues: List[CodeIssue] = []
        self.code_lines: List[str] = []

    def analyze_file(self, file_path: str) -> List[CodeIssue]:
        """
        Perform comprehensive fault-tolerant analysis of a Python file.

        Args:
            file_path: Path to the Python file to analyze

        Returns:
            List of all issues found during analysis
        """
        self.issues = []

        if not os.path.exists(file_path):
            self.issues.append(CodeIssue("error", "file", 0, 0, f"File not found: {file_path}"))
            return self.issues

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                self.code_lines = content.splitlines()
        except Exception as e:
            self.issues.append(CodeIssue("error", "file", 0, 0, f"Cannot read file: {str(e)}"))
            return self.issues

        # Pass 1: Syntax Analysis
        self._analyze_syntax(content)

        # Pass 2: Import Analysis (even if syntax errors exist)
        self._analyze_imports(content)

        # Pass 3: Name Resolution Analysis
        self._analyze_names(content)

        # Pass 4: Manim-specific Analysis
        self._analyze_manim_specific(content)

        # Pass 5: Smart Execution Analysis (New!)
        self._analyze_smart_execution(content, file_path)

        return self.issues

    def _analyze_syntax(self, content: str):
        """Analyze syntax using AST, with error recovery."""
        try:
            ast.parse(content)
        except SyntaxError as e:
            self.issues.append(CodeIssue(
                "error", "syntax", e.lineno or 0, e.offset or 0,
                f"Syntax error: {e.msg}",
                self._get_code_context(e.lineno or 0)
            ))

            # Try to analyze individual lines or blocks for more syntax errors
            self._analyze_syntax_by_segments(content)

    def _analyze_syntax_by_segments(self, content: str):
        """Try to find additional syntax errors by analyzing code segments."""
        lines = content.splitlines()

        # Try to parse function definitions individually
        current_function = []
        in_function = False
        function_start_line = 0

        for i, line in enumerate(lines, 1):
            stripped = line.strip()

            if stripped.startswith('def ') or stripped.startswith('class '):
                if current_function and in_function:
                    # Try to parse the previous function
                    self._try_parse_segment('\n'.join(current_function), function_start_line)

                current_function = [line]
                in_function = True
                function_start_line = i
            elif in_function:
                current_function.append(line)

                # Check if function/class ended (next non-indented line)
                if stripped and not line.startswith(' ') and not line.startswith('\t'):
                    if not (stripped.startswith('def ') or stripped.startswith('class ')):
                        # Function ended
                        self._try_parse_segment('\n'.join(current_function[:-1]), function_start_line)
                        current_function = [line]
                        in_function = False

        # Parse the last function if exists
        if current_function and in_function:
            self._try_parse_segment('\n'.join(current_function), function_start_line)

    def _try_parse_segment(self, segment: str, start_line: int):
        """Try to parse a code segment and report any syntax errors."""
        try:
            ast.parse(segment)
        except SyntaxError as e:
            actual_line = start_line + (e.lineno or 1) - 1
            self.issues.append(CodeIssue(
                "error", "syntax", actual_line, e.offset or 0,
                f"Syntax error in code segment: {e.msg}",
                self._get_code_context(actual_line)
            ))

    def _analyze_imports(self, content: str):
        """Analyze import statements, even with syntax errors present."""
        lines = content.splitlines()

        for i, line in enumerate(lines, 1):
            stripped = line.strip()

            # Check for import statements
            if stripped.startswith('import ') or stripped.startswith('from '):
                try:
                    # Try to parse just this import line
                    ast.parse(line)

                    # Check if the module can actually be imported
                    self._validate_import_line(stripped, i)

                except SyntaxError as e:
                    self.issues.append(CodeIssue(
                        "error", "import", i, e.offset or 0,
                        f"Invalid import syntax: {e.msg}",
                        line
                    ))

    def _validate_import_line(self, import_line: str, line_num: int):
        """Validate if an import can actually be resolved."""
        try:
            if import_line.startswith('from '):
                # Parse "from module import name"
                match = re.match(r'from\s+([^\s]+)\s+import\s+(.+)', import_line)
                if match:
                    module_name = match.group(1)
                    if module_name not in ['manim', '__future__'] and '.' not in module_name:
                        # Try to find the module
                        try:
                            importlib.util.find_spec(module_name)
                        except (ImportError, ModuleNotFoundError, ValueError):
                            self.issues.append(CodeIssue(
                                "warning", "import", line_num, 0,
                                f"Module '{module_name}' may not be available",
                                import_line
                            ))
            elif import_line.startswith('import '):
                # Parse "import module"
                module_name = import_line[7:].split()[0].split('.')[0]
                if module_name not in ['manim', 'math', 'os', 'sys']:
                    try:
                        importlib.util.find_spec(module_name)
                    except (ImportError, ModuleNotFoundError, ValueError):
                        self.issues.append(CodeIssue(
                            "warning", "import", line_num, 0,
                            f"Module '{module_name}' may not be available",
                            import_line
                        ))
        except Exception:
            # If import validation fails, it's not critical
            pass

    def _analyze_names(self, content: str):
        """Analyze name usage and definition, with error recovery."""
        try:
            tree = ast.parse(content)

            # Collect defined names
            defined_names = set()
            used_names = set()

            for node in ast.walk(tree):
                if isinstance(node, ast.Name):
                    if isinstance(node.ctx, ast.Store):
                        defined_names.add(node.id)
                    elif isinstance(node.ctx, ast.Load):
                        used_names.add((node.id, getattr(node, 'lineno', 0)))

            # Check for undefined names
            builtin_names = {
                'self', 'super', 'len', 'range', 'str', 'int', 'float', 'list', 'dict', 'set', 'tuple',
                'print', 'input', 'open', 'abs', 'min', 'max', 'sum', 'any', 'all', 'enumerate', 'zip'
            }

            manim_names = {
                'Scene', 'MovingCameraScene', 'ThreeDScene', 'Text', 'MathTex', 'Tex', 'Circle', 'Square',
                'Rectangle', 'Line', 'Arrow', 'Dot', 'VGroup', 'UP', 'DOWN', 'LEFT', 'RIGHT', 'ORIGIN',
                'PI', 'TAU', 'RED', 'BLUE', 'GREEN', 'YELLOW', 'WHITE', 'BLACK', 'Create', 'Write',
                'FadeIn', 'FadeOut', 'Transform', 'ReplacementTransform', 'AnimationGroup', 'Succession'
            }

            for name, line_num in used_names:
                if (name not in defined_names and
                    name not in builtin_names and
                    name not in manim_names and
                    not name.startswith('_') and
                    name[0].islower()):

                    self.issues.append(CodeIssue(
                        "warning", "name", line_num, 0,
                        f"Potentially undefined variable: '{name}'",
                        self._get_code_context(line_num)
                    ))

        except SyntaxError:
            # If we can't parse the AST, skip name analysis
            pass

    def _analyze_manim_specific(self, content: str):
        """Analyze Manim-specific patterns and common errors."""
        lines = content.splitlines()

        # Check for Scene class and construct method
        has_scene_class = False
        scene_classes = []

        for i, line in enumerate(lines, 1):
            stripped = line.strip()

            # Check for Scene class definition
            if re.match(r'class\s+\w+.*Scene.*:', stripped):
                has_scene_class = True
                class_name = re.match(r'class\s+(\w+)', stripped).group(1)
                scene_classes.append((class_name, i))

            # Check for common Manim errors
            if '.add()' in line or '.add( )' in line:
                self.issues.append(CodeIssue(
                    "error", "manim", i, 0,
                    "Empty add() call - must provide objects to add",
                    line.strip()
                ))

            if '.play()' in line or '.play( )' in line:
                self.issues.append(CodeIssue(
                    "error", "manim", i, 0,
                    "Empty play() call - must provide animations",
                    line.strip()
                ))

            if 'font_size=' in line and ('"' in line or "'" in line):
                if re.search(r'font_size\s*=\s*["\'][^"\']*["\']', line):
                    self.issues.append(CodeIssue(
                        "error", "manim", i, 0,
                        "font_size should be numeric, not string",
                        line.strip()
                    ))

        if not has_scene_class:
            self.issues.append(CodeIssue(
                "error", "manim", 0, 0,
                "No Scene class found - Manim requires at least one Scene class"
            ))

        # Check for construct method in each Scene class
        for class_name, class_line in scene_classes:
            has_construct = False
            for i, line in enumerate(lines[class_line:], class_line + 1):
                if line.strip().startswith('def construct('):
                    has_construct = True
                    break
                elif line.strip().startswith('class ') and i > class_line:
                    # Next class started
                    break

            if not has_construct:
                self.issues.append(CodeIssue(
                    "error", "manim", class_line, 0,
                    f"Scene class '{class_name}' missing construct() method"
                ))

    def _analyze_smart_execution(self, content: str, file_path: str):
        """Analyze code using smart execution in mock environment."""
        try:
            smart_analyzer = SmartExecutionAnalyzer()
            smart_issues = smart_analyzer.analyze_with_smart_execution(content, file_path)

            # Add smart execution issues to main issues list
            self.issues.extend(smart_issues)

        except Exception as e:
            self.issues.append(CodeIssue(
                "warning", "smart_execution", 0, 0,
                f"Smart execution analysis failed: {str(e)}"
            ))



    def _get_code_context(self, line_num: int, context_lines: int = 2) -> str:
        """Get code context around a specific line."""
        if not self.code_lines or line_num <= 0:
            return ""

        start = max(0, line_num - context_lines - 1)
        end = min(len(self.code_lines), line_num + context_lines)

        context = []
        for i in range(start, end):
            marker = ">>> " if i == line_num - 1 else "    "
            context.append(f"{marker}{i+1:3d}: {self.code_lines[i]}")

        return '\n'.join(context)


class MockManim:
    """Mock Manim environment for safe code execution."""

    def __init__(self):
        self.created_objects = {}
        self.method_calls = []

    def create_mock_object(self, name: str, obj_type: str = "mobject"):
        """Create a mock Manim object that can be used safely."""
        mock_obj = MagicMock()
        mock_obj._mock_name = name
        mock_obj._mock_type = obj_type

        # Add common Manim methods
        mock_obj.shift = MagicMock(return_value=mock_obj)
        mock_obj.scale = MagicMock(return_value=mock_obj)
        mock_obj.rotate = MagicMock(return_value=mock_obj)
        mock_obj.move_to = MagicMock(return_value=mock_obj)
        mock_obj.next_to = MagicMock(return_value=mock_obj)
        mock_obj.set_color = MagicMock(return_value=mock_obj)

        self.created_objects[name] = mock_obj
        return mock_obj

    def get_mock_scene(self):
        """Create a mock Scene class."""
        scene_mock = MagicMock()
        scene_mock.add = MagicMock()
        scene_mock.play = MagicMock()
        scene_mock.wait = MagicMock()
        scene_mock.remove = MagicMock()
        scene_mock.clear = MagicMock()

        return scene_mock


class SmartExecutionAnalyzer:
    """
    Smart execution analyzer that runs code in a mock environment
    and continues execution even when errors occur.
    """

    def __init__(self):
        self.issues = []
        self.mock_manim = MockManim()
        self.execution_context = {}

    def analyze_with_smart_execution(self, content: str, file_path: str) -> List[CodeIssue]:
        """
        Analyze code by executing it in a smart mock environment.

        This method:
        1. Creates a mock Manim environment
        2. Executes code line by line
        3. When errors occur, creates placeholder objects and continues
        4. Distinguishes between real errors and dependency issues
        """
        self.issues = []
        lines = content.splitlines()

        # Create mock environment
        mock_globals = self._create_mock_environment()

        # Execute code line by line
        for line_num, line in enumerate(lines, 1):
            if line.strip() and not line.strip().startswith('#'):
                self._execute_line_safely(line, line_num, mock_globals)

        return self.issues

    def _create_mock_environment(self) -> dict:
        """Create a mock execution environment with Manim objects."""
        mock_env = {
            # Mock Manim classes
            'Scene': self._create_mock_class('Scene'),
            'MovingCameraScene': self._create_mock_class('MovingCameraScene'),
            'ThreeDScene': self._create_mock_class('ThreeDScene'),
            'Text': self._create_mock_class('Text'),
            'MathTex': self._create_mock_class('MathTex'),
            'Tex': self._create_mock_class('Tex'),
            'Circle': self._create_mock_class('Circle'),
            'Square': self._create_mock_class('Square'),
            'Rectangle': self._create_mock_class('Rectangle'),
            'Line': self._create_mock_class('Line'),
            'Arrow': self._create_mock_class('Arrow'),
            'Dot': self._create_mock_class('Dot'),
            'VGroup': self._create_mock_class('VGroup'),

            # Mock animations
            'Create': self._create_mock_class('Create'),
            'Write': self._create_mock_class('Write'),
            'FadeIn': self._create_mock_class('FadeIn'),
            'FadeOut': self._create_mock_class('FadeOut'),
            'Transform': self._create_mock_class('Transform'),
            'ReplacementTransform': self._create_mock_class('ReplacementTransform'),
            'AnimationGroup': self._create_mock_class('AnimationGroup'),
            'Succession': self._create_mock_class('Succession'),

            # Mock constants
            'UP': MagicMock(),
            'DOWN': MagicMock(),
            'LEFT': MagicMock(),
            'RIGHT': MagicMock(),
            'ORIGIN': MagicMock(),
            'PI': 3.14159,
            'TAU': 6.28318,
            'RED': MagicMock(),
            'BLUE': MagicMock(),
            'GREEN': MagicMock(),
            'YELLOW': MagicMock(),
            'WHITE': MagicMock(),
            'BLACK': MagicMock(),

            # Built-in functions
            '__builtins__': __builtins__,
        }

        return mock_env

    def _create_mock_class(self, class_name: str):
        """Create a mock class that returns mock objects."""
        def mock_constructor(*args, **kwargs):
            return self.mock_manim.create_mock_object(f"{class_name}_instance", class_name.lower())

        mock_constructor.__name__ = class_name
        return mock_constructor

    def _execute_line_safely(self, line: str, line_num: int, mock_globals: dict):
        """Execute a single line of code safely in the mock environment."""
        try:
            # Skip certain problematic patterns
            if any(skip in line for skip in ['import ', 'from ', 'class ', 'def ']):
                # Handle these specially
                self._handle_special_line(line, line_num, mock_globals)
                return

            # Try to execute the line
            exec(line, mock_globals)

        except NameError as e:
            # Variable not defined - try to create a placeholder
            var_name = self._extract_variable_name_from_error(str(e))
            if var_name:
                # Create a placeholder object
                placeholder = self.mock_manim.create_mock_object(f"placeholder_{var_name}", "placeholder")
                mock_globals[var_name] = placeholder

                self.issues.append(CodeIssue(
                    "warning", "name", line_num, 0,
                    f"Variable '{var_name}' used before definition - created placeholder",
                    line.strip()
                ))

                # Try to execute again with placeholder
                try:
                    exec(line, mock_globals)
                except Exception as e2:
                    self.issues.append(CodeIssue(
                        "error", "execution", line_num, 0,
                        f"Execution error even with placeholder: {str(e2)}",
                        line.strip()
                    ))
            else:
                self.issues.append(CodeIssue(
                    "error", "name", line_num, 0,
                    f"Name error: {str(e)}",
                    line.strip()
                ))

        except SyntaxError as e:
            self.issues.append(CodeIssue(
                "error", "syntax", line_num, e.offset or 0,
                f"Syntax error: {e.msg}",
                line.strip()
            ))

        except TypeError as e:
            self.issues.append(CodeIssue(
                "error", "type", line_num, 0,
                f"Type error: {str(e)}",
                line.strip()
            ))

        except AttributeError as e:
            # Try to handle attribute errors by creating mock attributes
            self._handle_attribute_error(e, line, line_num, mock_globals)

        except Exception as e:
            self.issues.append(CodeIssue(
                "error", "runtime", line_num, 0,
                f"Runtime error: {str(e)}",
                line.strip()
            ))

    def _handle_special_line(self, line: str, line_num: int, mock_globals: dict):
        """Handle special lines like imports, class definitions, etc."""
        stripped = line.strip()

        if stripped.startswith('class '):
            # Extract class name and create mock
            match = re.match(r'class\s+(\w+)', stripped)
            if match:
                class_name = match.group(1)
                mock_globals[class_name] = self._create_mock_class(class_name)

        elif stripped.startswith('def '):
            # Extract function name and create mock
            match = re.match(r'def\s+(\w+)', stripped)
            if match:
                func_name = match.group(1)
                mock_globals[func_name] = MagicMock()

    def _extract_variable_name_from_error(self, error_msg: str) -> Optional[str]:
        """Extract variable name from NameError message."""
        # NameError: name 'variable_name' is not defined
        match = re.search(r"name '(\w+)' is not defined", error_msg)
        return match.group(1) if match else None

    def _handle_attribute_error(self, error: AttributeError, line: str, line_num: int, mock_globals: dict):
        """Handle attribute errors by creating mock attributes."""
        error_msg = str(error)

        # Try to extract object and attribute names
        # AttributeError: 'MockObject' object has no attribute 'some_method'
        match = re.search(r"'(\w+)' object has no attribute '(\w+)'", error_msg)
        if match:
            obj_type, attr_name = match.groups()

            self.issues.append(CodeIssue(
                "warning", "attribute", line_num, 0,
                f"Attribute '{attr_name}' not found on {obj_type} - this might be a real error",
                line.strip()
            ))
        else:
            self.issues.append(CodeIssue(
                "error", "attribute", line_num, 0,
                f"Attribute error: {error_msg}",
                line.strip()
            ))

    def format_issues(self) -> str:
        """Format all found issues into a readable report."""
        if not self.issues:
            return "✅ No issues found in fault-tolerant analysis"

        # Group issues by severity
        errors = [issue for issue in self.issues if issue.severity == "error"]
        warnings = [issue for issue in self.issues if issue.severity == "warning"]
        infos = [issue for issue in self.issues if issue.severity == "info"]

        report = []
        report.append("🔍 Fault-Tolerant Python Analysis Results")
        report.append("=" * 50)

        if errors:
            report.append(f"\n❌ ERRORS ({len(errors)}):")
            for error in sorted(errors, key=lambda x: x.line):
                report.append(f"  {error}")
                if error.code_context:
                    report.append(f"    Context:\n{error.code_context}")

        if warnings:
            report.append(f"\n⚠️ WARNINGS ({len(warnings)}):")
            for warning in sorted(warnings, key=lambda x: x.line):
                report.append(f"  {warning}")

        if infos:
            report.append(f"\nℹ️ INFO ({len(infos)}):")
            for info in sorted(infos, key=lambda x: x.line):
                report.append(f"  {info}")

        report.append(f"\n📊 Summary: {len(errors)} errors, {len(warnings)} warnings, {len(infos)} info")

        return '\n'.join(report)


def analyze_python_file_fault_tolerant(file_path: str) -> str:
    """
    Perform fault-tolerant analysis of a Python file.

    This function analyzes Python code similar to how C++ compilers work:
    it continues analysis even when errors are encountered, providing a
    comprehensive report of all issues found.

    Args:
        file_path: Path to the Python file to analyze

    Returns:
        Formatted report of all issues found
    """
    analyzer = FaultTolerantPythonAnalyzer()
    issues = analyzer.analyze_file(file_path)
    return analyzer.format_issues()
