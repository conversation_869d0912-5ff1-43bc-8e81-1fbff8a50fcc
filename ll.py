import os
# os.environ["MOONSHOT_API_KEY"] = "sk-Lp2Gk32pn7RBia9i0U58tipQ5E4eEiXdPKAyp5lIYP2bAJVv"  # your Moonshot AI API key

import litellm
from litellm import completion


messages = [{"content": "Hello, how are you?", "role": "user"}]

# Moonshot call
response = completion(
    model="openai/kimi-k2-0711-preview",
    api_base="https://api.moonshot.cn/v1",
    api_key="sk-Lp2Gk32pn7RBia9i0U58tipQ5E4eEiXdPKAyp5lIYP2bAJVv",
    messages=messages
)

print(response)
