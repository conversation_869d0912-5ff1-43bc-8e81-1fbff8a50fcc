# Prompt评估工具使用指南

这个工具可以单独使用优化后的prompt来生成教学例子，并用大模型评估其质量。

## 文件说明

- `prompt_evaluation_tool.py` - 主要的评估工具类
- `test_prompt_evaluation.py` - 测试脚本和使用示例
- `README_prompt_evaluation.md` - 本使用指南

## 功能特点

### 1. 单独Prompt评估
- 使用优化后的 `prompts/example_gen_prompt.py` 生成教学内容
- 专门针对同构类比思维和边界意识进行评估
- 支持多维度质量评估（总分100分）

### 2. 评估维度

**同构类比质量（25分）**
- 结构同构性：类比与目标概念的内在逻辑结构一致性
- 观众熟悉度：类比是否选择了目标受众熟悉的领域
- 认知桥梁效果：从熟悉领域到陌生概念的过渡效果

**教学设计完整性（25分）**
- 叙事结构完整性：完整的教学流程设计
- 逻辑连贯性：各分镜间的逻辑关系
- 内容深度适宜性：内容深度与目标受众的匹配度

**边界意识与适用性（20分）**
- 局限性识别：概念适用边界的系统性识别
- 误用场景警示：典型误用场景的警示
- 替代方案提及：不适用场景下的正确替代方案

**步骤总结可操作性（15分）**
- 步骤提炼合理性：核心步骤的提炼质量
- 动画系统兼容性：与animate_step_by_step函数的兼容性
- 实用性：实际教学指导价值

**视觉化描述质量（15分）**
- 动画描述具体性：视觉变换描述的具体程度
- Manim适配性：与Manim动画制作的适配性
- 教学效果支撑：视觉设计对教学目标的支撑

## 使用方法

### 1. 命令行使用

```bash
# 基本用法
python tools/prompt_evaluation_tool.py "注意力机制" "帮助机器学习初学者理解注意力机制的核心原理" "有基础编程经验但对深度学习了解有限的开发者"

# 指定自定义prompt文件
python tools/prompt_evaluation_tool.py "BERT模型" "向NLP工程师介绍BERT的预训练机制" "有自然语言处理经验的工程师" --prompt-file "prompts/custom_prompt.py"
```

### 2. Python脚本使用

```python
from tools.prompt_evaluation_tool import PromptEvaluationTool

# 创建评估工具
tool = PromptEvaluationTool()

# 运行评估
result = tool.run_evaluation(
    topic="Transformer架构",
    purpose="向AI研究者介绍Transformer的核心创新点", 
    audience="有深度学习基础的研究人员"
)

# 打印结果摘要
tool.print_evaluation_summary(result)
```

### 3. 运行测试脚本

```bash
# 运行完整测试套件
python tools/test_prompt_evaluation.py

# 这将执行：
# - 基本功能测试
# - 多主题测试
# - 自定义prompt测试
# - 趋势分析
```

## 输出文件

评估结果会保存在 `output/prompt_evaluations/` 目录下：

```
output/prompt_evaluations/
├── 注意力机制/
│   ├── evaluation_20241226_143022.json  # 详细评估结果
│   └── example_20241226_143022.md       # 生成的教学例子
├── Transformer架构/
│   ├── evaluation_20241226_143156.json
│   └── example_20241226_143156.md
└── ...
```

## 评估结果示例

```json
{
  "overall_score": 85,
  "grade": "优秀",
  "detailed_scores": {
    "analogical_thinking_quality": {
      "score": 22,
      "max_score": 25,
      "analysis": "类比选择恰当，结构同构性强..."
    },
    "instructional_design_completeness": {
      "score": 20,
      "max_score": 25,
      "analysis": "教学流程完整，逻辑清晰..."
    }
  },
  "strengths": ["同构类比选择精准", "边界意识清晰"],
  "weaknesses": ["视觉描述可以更具体"],
  "improvement_suggestions": ["增强动画描述的可操作性"],
  "overall_assessment": "整体质量优秀，特别是在同构类比方面表现突出"
}
```

## 配置要求

确保以下文件存在并正确配置：

1. `config/config.yaml` - 模型配置文件
2. `prompts/example_gen_prompt.py` - 优化后的prompt文件
3. 相关依赖包已安装（camel-ai, loguru, yaml等）

## 注意事项

1. **模型配置**：确保在 `config/config.yaml` 中正确配置了LLM模型
2. **Prompt文件**：默认使用 `prompts/example_gen_prompt.py`，可通过参数指定其他文件
3. **输出目录**：结果会自动保存，确保有写入权限
4. **日志记录**：评估过程会记录到 `logs/prompt_evaluation.log`

## 扩展功能

可以通过修改 `PromptEvaluationTool` 类来：

1. 添加新的评估维度
2. 调整评分权重
3. 自定义输出格式
4. 集成其他评估模型

## 故障排除

**常见问题：**

1. **模型连接失败**：检查 `config/config.yaml` 中的模型配置
2. **Prompt文件不存在**：确认文件路径正确
3. **权限错误**：确保对输出目录有写入权限
4. **依赖包缺失**：运行 `pip install -r requirements.txt`

**调试方法：**

1. 查看日志文件 `logs/prompt_evaluation.log`
2. 使用测试脚本验证基本功能
3. 检查生成的中间文件是否正常
