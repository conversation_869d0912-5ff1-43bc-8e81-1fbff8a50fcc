#!/usr/bin/env python3
"""
定理解释工作流 - Claude Code Router版本
演示如何使用CCR替代原有的scene_code_generation_agent
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 使用CCR集成模块替代原有的scene_code_generation_agent
from scripts.ccr_integration import process_scene_file_enhanced_ccr as process_scene_file_enhanced

# 其他导入保持不变
from agents.theorem_agents.animation_narration_agent import AnimationNarrationAgent
from agents.theorem_agents.code_generation_agent import CodeGenerationAgent
from agents.theorem_agents.scene_plan_agent import ScenePlanAgent
from agents.theorem_agents.technical_implementation_agent import TechnicalImplementationAgent
from agents.theorem_agents.vision_storyboard_agent import VisionStoryboardAgent


def demo_ccr_integration():
    """演示CCR集成的使用"""
    print("🎬 定理解释工作流 - CCR版本演示")
    print("=" * 60)
    
    # 示例场景描述
    scene_description = """
    创建一个关于勾股定理的动画场景：
    
    1. 显示标题"勾股定理"，使用大号蓝色字体
    2. 绘制一个直角三角形，边长分别为3、4、5
    3. 标注三边长度：a=3, b=4, c=5
    4. 显示公式：a² + b² = c²
    5. 逐步计算：3² + 4² = 9 + 16 = 25 = 5²
    6. 高亮显示等式成立
    
    要求：
    - 使用清晰的颜色区分不同元素
    - 动画要有节奏感，不要太快
    - 确保文本使用Text类，数学公式使用MathTex
    """
    
    # 使用CCR处理场景
    print("🚀 使用Claude Code Router生成Manim代码...")
    
    result = process_scene_file_enhanced(
        scene_description=scene_description,
        output_dir="output/ccr_demo",
        max_iterations=3,
        quality="l",
        scene_num=1,
        topic="pythagorean_theorem"
    )
    
    # 输出结果
    print("\n📊 生成结果:")
    if result:
        print(f"✅ 成功状态: {result.get('success')}")
        print(f"📄 代码文件: {result.get('final_code_path')}")
        print(f"🎥 视频文件: {result.get('final_video_path')}")
        
        if result.get('success'):
            print("\n🎉 CCR集成演示成功！")
            print("生成的代码和视频可以在输出目录中找到。")
        else:
            print("\n⚠️ 代码生成成功但视频渲染可能失败")
    else:
        print("❌ CCR处理失败")
        return False
    
    return True


def compare_with_original():
    """比较CCR版本与原版本的差异"""
    print("\n🔍 CCR版本 vs 原版本对比:")
    print("=" * 60)
    
    print("CCR版本优势:")
    print("✅ 自动安装和配置CCR工具")
    print("✅ 集成常见错误知识，减少重复错误")
    print("✅ 优化的提示词，提高生成质量")
    print("✅ 完全兼容原有接口")
    print("✅ 更稳定的代码生成")
    
    print("\n原版本特点:")
    print("🔧 基于自定义的多代理框架")
    print("🔧 支持多次迭代优化")
    print("🔧 详细的调试和日志信息")
    print("🔧 可自定义工具链")
    
    print("\n💡 建议:")
    print("- 对于简单场景，推荐使用CCR版本")
    print("- 对于复杂场景或需要精细控制，可使用原版本")
    print("- 可以根据具体需求选择合适的版本")


if __name__ == "__main__":
    # 运行演示
    success = demo_ccr_integration()
    
    # 显示对比信息
    compare_with_original()
    
    if success:
        print("\n🎊 演示完成！")
    else:
        print("\n💥 演示失败！")
        sys.exit(1)
