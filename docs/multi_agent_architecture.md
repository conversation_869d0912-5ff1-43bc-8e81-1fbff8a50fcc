# Multi-Agent Scene Code Generator

## 概述

Multi-Agent Scene Code Generator 是一个基于 smolagents 的多智能体架构，专门设计来解决传统单一 agent 中 memory 累积导致的上下文过长问题。

## 问题背景

在传统的代码生成 agent 中，存在以下问题：

1. **Memory 累积问题**：agent 内部会累积所有的 memory step
2. **上下文过长**：下一次调用大模型时将所有 memory step 和当前 observation 都拼接起来作为输入
3. **性能下降**：导致 input 越来越长，影响 LLM 的理解和响应速度
4. **成本增加**：更长的输入意味着更高的 API 调用成本

## 解决方案

### Multi-Agent 架构设计

我们设计了一个专门的多智能体系统，包含四个专门的 agent：

```
┌─────────────────┐
│   Manager       │  ← 任务协调和高级决策
│   Agent         │
└─────────────────┘
         │
    ┌────┴────┐
    │         │
┌───▼───┐ ┌──▼──┐ ┌────────────┐
│Code   │ │Code │ │Memory      │
│Gen    │ │Fix  │ │Agent       │
│Agent  │ │Agent│ │            │
└───────┘ └─────┘ └────────────┘
```

### 核心组件

#### 1. ManagerAgent (管理者)
- **职责**：任务协调和高级决策
- **特点**：维护高级任务状态，不需要详细的执行历史
- **工具**：无直接工具，通过管理其他 agents 工作

#### 2. CodeFixerAgent (代码修复专家) ⭐ 核心组件
- **职责**：专门修复 Python/Manim 代码问题
- **输入**：当前代码 + 错误信息 + 诊断结果（精简上下文）
- **特点**：每次都是"新鲜"开始，无累积 memory 负担
- **工具**：文件操作、代码诊断、执行验证、文档查询

#### 3. CodeGeneratorAgent (代码生成专家)
- **职责**：从描述生成初始 Manim 代码
- **输入**：动画描述 + 要求 + 参考文档
- **特点**：专注于高质量初始代码生成
- **工具**：文件创建、文档查询、代码搜索

#### 4. MemoryAgent (记忆管理专家)
- **职责**：从对话历史中提取经验并更新记忆文件
- **输入**：对话历史摘要
- **特点**：独立管理经验总结，不影响其他 agents
- **工具**：文件读写（仅限记忆文件）

## 关键优势

### 1. 解决 Memory 累积问题
- ✅ **精简上下文**：每个专门 agent 只接收任务相关的最小上下文
- ✅ **无历史负担**：CodeFixerAgent 不需要了解整个开发历史
- ✅ **独立执行**：每个任务都是独立的，避免上下文污染

### 2. 提高效率和专注度
- ✅ **专业分工**：每个 agent 专注于特定任务类型
- ✅ **并行处理**：可以同时处理多个不同类型的任务
- ✅ **更好调试**：问题定位更精确，调试更容易

### 3. 成本和性能优化
- ✅ **减少 Token 消耗**：精简的上下文大幅减少 API 调用成本
- ✅ **更快响应**：更短的输入获得更快的 LLM 响应
- ✅ **更好理解**：LLM 能更好地理解精简的任务描述

## 使用方法

### 基本初始化

```python
from agents.multi_agent_scene_code_generator import MultiAgentSceneCodeGenerator

# 初始化多智能体系统（与现有agent完全相同的接口）
multi_agent = MultiAgentSceneCodeGenerator(
    working_dir="output"  # 可选，默认为当前目录
)

# 配置从config文件自动读取，与现有agent保持一致
# 包括：model配置、API密钥、工具开关、最大迭代次数等
```

### 代码修复（核心功能）

```python
from agents.multi_agent_scene_code_generator import CodeFixingTask

# 创建代码修复任务
task = CodeFixingTask(
    file_path="scene.py",
    error_log="NameError: name 'Circl' is not defined",
    diagnostic_results="Line 8: 'Circl' should be 'Circle'",
    current_code=open("scene.py").read(),
    context_info="Simple Manim scene with typo"
)

# 执行修复（精简上下文，无 memory 累积）
result = multi_agent.fix_code(task)

if result.success:
    print("✅ 代码修复成功！")
    print(f"修改的文件：{result.files_modified}")
else:
    print(f"❌ 修复失败：{result.error_message}")
```

### 代码生成

```python
from agents.multi_agent_scene_code_generator import CodeGenerationTask

# 创建代码生成任务
task = CodeGenerationTask(
    description="创建一个圆形从左移动到右的动画",
    requirements=["使用 Manim 最新 API", "动画时长 3 秒"],
    output_path="output/animation.py"
)

# 生成代码
result = multi_agent.generate_code(task)
```

### 记忆更新

```python
# 更新编程经验记忆
conversation_history = """
用户遇到了 NameError: 'Circl' 问题
解决方案：将 'Circl' 改为 'Circle'
经验：Manim 类名拼写错误是常见问题
"""

result = multi_agent.update_memory(conversation_history)
```

### 管理者协调

```python
# 让管理者决定使用哪个专门的 agent
task_description = """
我需要创建一个数学公式变换的动画，
显示 x^2 + 2x + 1 = (x + 1)^2 的过程
"""

result = multi_agent.run_with_manager(task_description)
```

### 完全兼容现有接口

```python
# 与现有agent完全相同的使用方式
multi_agent = MultiAgentSceneCodeGenerator(working_dir="output")

# 1. 使用现有的代码生成接口
result = multi_agent.generate_manim_code_enhanced(
    scene_description="创建一个显示数学函数的动画",
    output_file="output/math_scene.py",
    max_iterations=3
)

# 2. 使用现有的编程任务接口
result = multi_agent.execute_programming_task(
    task_description="生成一个圆形移动的动画",
    output_file="output/circle_animation.py",
    max_iterations=3
)

# 3. 使用现有的视频渲染接口
if result:
    video_path = multi_agent.render_manim_code(result, quality="l")
    print(f"视频已生成：{video_path}")
```

## 配置选项

### 完全兼容现有配置系统
Multi-Agent架构使用与现有agent完全相同的配置系统，从`utils/common.py`的Config类读取配置：

```yaml
# config.yaml 示例
workflow:
  code_agent:
    enable_sequential_thinking: true
    enable_get_docs: true
    enable_memory: true
    max_iteration_per_step: 20
    model: "moonshotai/kimi-k2"
    memory_model: "google/gemini-2.5-flash-lite-preview-06-17"
    memory_file_path: "multi_agent_memory.md"

model:
  type: "moonshotai/kimi-k2"
  api:
    openrouter_api_key: "your-api-key"
    openrouter_api_base_url: "https://openrouter.ai/api/v1"
  max_tokens: 32768
```

### 向后兼容性
- ✅ 相同的初始化接口：`MultiAgentSceneCodeGenerator(working_dir="path")`
- ✅ 相同的方法签名：`execute_programming_task()`, `generate_manim_code_enhanced()`, `render_manim_code()`
- ✅ 相同的配置读取：从config文件自动加载所有设置
- ✅ 相同的工具集成：sequential_thinking, get_docs, memory等
- ✅ 相同的视频渲染：支持所有质量级别(l/m/h/q/k)

## 与传统方案对比

| 特性 | 传统单一 Agent | Multi-Agent 架构 |
|------|----------------|------------------|
| 上下文长度 | 累积增长 ❌ | 精简固定 ✅ |
| Memory 管理 | 累积所有历史 ❌ | 按需最小化 ✅ |
| 任务专注度 | 混合处理 ❌ | 专业分工 ✅ |
| 调试难度 | 复杂 ❌ | 简单 ✅ |
| 成本控制 | 难以控制 ❌ | 精确控制 ✅ |
| 并行能力 | 无 ❌ | 支持 ✅ |

## 最佳实践

### 1. 任务分解
- 将复杂任务分解为具体的子任务
- 为每个 agent 准备精确的任务描述
- 避免在单个任务中混合多种操作

### 2. 上下文控制
- 只提供任务必需的信息
- 避免传递不相关的历史信息
- 使用结构化的任务格式

### 3. 错误处理
- 每个 agent 都有独立的错误处理
- 失败的任务不会影响其他 agents
- 可以重试特定的 agent 而不是整个系统

### 4. 性能优化
- 为不同 agents 使用不同规模的模型
- 记忆 agent 可以使用更便宜的模型
- 根据任务复杂度调整 max_steps

## 示例和测试

- 查看 `examples/multi_agent_example.py` 了解完整使用示例
- 运行 `python test_multi_agent.py` 进行基本功能测试
- 查看 `output/` 目录中的生成结果

## 总结

Multi-Agent Scene Code Generator 通过专业分工和精简上下文的设计，彻底解决了传统 agent 的 memory 累积问题，同时提供了更好的性能、更低的成本和更高的可维护性。特别是 CodeFixerAgent 的设计，让代码问题修复变得更加高效和专注。
