#!/usr/bin/env python3
"""
Compatibility Test: Multi-Agent vs Existing Agent

This script tests the compatibility between the new multi-agent architecture
and the existing smolagents agent to ensure they have the same interface.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_interface_compatibility():
    """Test that both agents have the same interface"""
    print("Testing interface compatibility...")
    
    try:
        # Import both agents
        from agents.multi_agent_scene_code_generator import MultiAgentSceneCodeGenerator
        from agents.smolagents_scene_code_generation_agent import SmolagentsUniversalCodingAgent
        
        # Test initialization with same parameters
        working_dir = "output/compatibility_test"
        
        multi_agent = MultiAgentSceneCodeGenerator(working_dir=working_dir)
        existing_agent = SmolagentsUniversalCodingAgent(working_dir=working_dir)
        
        print("✅ Both agents initialized successfully")
        
        # Test that both have the same key methods
        required_methods = [
            'execute_programming_task',
            'generate_manim_code_enhanced', 
            'render_manim_code'
        ]
        
        for method_name in required_methods:
            if hasattr(multi_agent, method_name) and hasattr(existing_agent, method_name):
                print(f"✅ Both agents have method: {method_name}")
            else:
                print(f"❌ Method missing: {method_name}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Interface compatibility test failed: {e}")
        return False


def test_configuration_compatibility():
    """Test that both agents read configuration the same way"""
    print("\nTesting configuration compatibility...")
    
    try:
        from agents.multi_agent_scene_code_generator import MultiAgentSceneCodeGenerator
        from agents.smolagents_scene_code_generation_agent import SmolagentsUniversalCodingAgent
        
        multi_agent = MultiAgentSceneCodeGenerator()
        existing_agent = SmolagentsUniversalCodingAgent()
        
        # Test that both read the same configuration values
        config_attributes = [
            'enable_sequential_thinking',
            'enable_get_docs',
            'enable_memory',
            'model_type',
            'memory_model_type'
        ]
        
        for attr in config_attributes:
            if hasattr(multi_agent, attr) and hasattr(existing_agent, attr):
                multi_value = getattr(multi_agent, attr)
                existing_value = getattr(existing_agent, attr)
                if multi_value == existing_value:
                    print(f"✅ {attr}: {multi_value} (same)")
                else:
                    print(f"⚠️  {attr}: multi={multi_value}, existing={existing_value} (different)")
            else:
                print(f"❌ Attribute missing: {attr}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration compatibility test failed: {e}")
        return False


def test_method_signatures():
    """Test that method signatures are compatible"""
    print("\nTesting method signatures...")
    
    try:
        from agents.multi_agent_scene_code_generator import MultiAgentSceneCodeGenerator
        from agents.smolagents_scene_code_generation_agent import SmolagentsUniversalCodingAgent
        import inspect
        
        multi_agent = MultiAgentSceneCodeGenerator()
        existing_agent = SmolagentsUniversalCodingAgent()
        
        # Test key method signatures
        methods_to_test = [
            'execute_programming_task',
            'generate_manim_code_enhanced',
            'render_manim_code'
        ]
        
        for method_name in methods_to_test:
            if hasattr(multi_agent, method_name) and hasattr(existing_agent, method_name):
                multi_sig = inspect.signature(getattr(multi_agent, method_name))
                existing_sig = inspect.signature(getattr(existing_agent, method_name))
                
                if str(multi_sig) == str(existing_sig):
                    print(f"✅ {method_name}: signatures match")
                else:
                    print(f"⚠️  {method_name}: signatures differ")
                    print(f"    Multi-agent: {multi_sig}")
                    print(f"    Existing:    {existing_sig}")
            else:
                print(f"❌ Method missing: {method_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Method signature test failed: {e}")
        return False


def test_memory_accumulation_solution():
    """Test that the multi-agent architecture solves memory accumulation"""
    print("\nTesting memory accumulation solution...")
    
    try:
        from agents.multi_agent_scene_code_generator import MultiAgentSceneCodeGenerator
        
        multi_agent = MultiAgentSceneCodeGenerator()
        
        # Check that the multi-agent system has specialized agents
        required_agents = ['code_fixer_agent', 'code_generator_agent', 'memory_agent', 'manager_agent']
        
        for agent_name in required_agents:
            if hasattr(multi_agent, agent_name):
                print(f"✅ Has specialized agent: {agent_name}")
            else:
                print(f"❌ Missing specialized agent: {agent_name}")
                return False
        
        # Check that the system reports the memory solution
        status = multi_agent.get_status()
        if "memory_accumulation_solution" in status:
            print(f"✅ Memory accumulation solution: {status['memory_accumulation_solution']}")
        else:
            print(f"❌ No memory accumulation solution reported")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Memory accumulation solution test failed: {e}")
        return False


def main():
    """Run all compatibility tests"""
    print("🧪 Multi-Agent vs Existing Agent Compatibility Tests")
    print("=" * 60)
    
    tests = [
        test_interface_compatibility,
        test_configuration_compatibility,
        test_method_signatures,
        test_memory_accumulation_solution,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 60)
    print(f"Compatibility Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 Full compatibility confirmed!")
        print("\nKey achievements:")
        print("✅ Same interface as existing agent")
        print("✅ Same configuration system")
        print("✅ Same method signatures")
        print("✅ Solves memory accumulation problem")
        print("✅ Drop-in replacement ready!")
    else:
        print("⚠️  Some compatibility issues found")
        print("The multi-agent architecture may need adjustments")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
